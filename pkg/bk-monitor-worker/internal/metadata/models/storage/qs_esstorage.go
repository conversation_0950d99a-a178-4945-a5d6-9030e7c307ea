// Code generated by go-queryset. DO NOT EDIT.
package storage

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set ESStorageQuerySet

// ESStorageQuerySet is an queryset type for ESStorage
type ESStorageQuerySet struct {
	db *gorm.DB
}

// NewESStorageQuerySet constructs new ESStorageQuerySet
func NewESStorageQuerySet(db *gorm.DB) ESStorageQuerySet {
	return ESStorageQuerySet{
		db: db.Model(&ESStorage{}),
	}
}

func (qs ESStorageQuerySet) w(db *gorm.DB) ESStorageQuerySet {
	return NewESStorageQuerySet(db)
}

func (qs ESStorageQuerySet) Select(fields ...ESStorageDBSchemaField) ESStorageQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *ESStorage) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *ESStorage) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) All(ret *[]ESStorage) error {
	return qs.db.Find(ret).Error
}

// Count is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// DateFormatEq is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) DateFormatEq(dateFormat string) ESStorageQuerySet {
	return qs.w(qs.db.Where("date_format = ?", dateFormat))
}

// DateFormatGt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) DateFormatGt(dateFormat string) ESStorageQuerySet {
	return qs.w(qs.db.Where("date_format > ?", dateFormat))
}

// DateFormatGte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) DateFormatGte(dateFormat string) ESStorageQuerySet {
	return qs.w(qs.db.Where("date_format >= ?", dateFormat))
}

// DateFormatIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) DateFormatIn(dateFormat ...string) ESStorageQuerySet {
	if len(dateFormat) == 0 {
		qs.db.AddError(errors.New("must at least pass one dateFormat in DateFormatIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("date_format IN (?)", dateFormat))
}

// DateFormatLike is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) DateFormatLike(dateFormat string) ESStorageQuerySet {
	return qs.w(qs.db.Where("date_format LIKE ?", dateFormat))
}

// DateFormatLt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) DateFormatLt(dateFormat string) ESStorageQuerySet {
	return qs.w(qs.db.Where("date_format < ?", dateFormat))
}

// DateFormatLte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) DateFormatLte(dateFormat string) ESStorageQuerySet {
	return qs.w(qs.db.Where("date_format <= ?", dateFormat))
}

// DateFormatNe is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) DateFormatNe(dateFormat string) ESStorageQuerySet {
	return qs.w(qs.db.Where("date_format != ?", dateFormat))
}

// DateFormatNotIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) DateFormatNotIn(dateFormat ...string) ESStorageQuerySet {
	if len(dateFormat) == 0 {
		qs.db.AddError(errors.New("must at least pass one dateFormat in DateFormatNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("date_format NOT IN (?)", dateFormat))
}

// DateFormatNotlike is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) DateFormatNotlike(dateFormat string) ESStorageQuerySet {
	return qs.w(qs.db.Where("date_format NOT LIKE ?", dateFormat))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) Delete() error {
	return qs.db.Delete(ESStorage{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(ESStorage{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(ESStorage{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) GetUpdater() ESStorageUpdater {
	return NewESStorageUpdater(qs.db)
}

// IndexSettingsEq is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) IndexSettingsEq(indexSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("index_settings = ?", indexSettings))
}

// IndexSettingsGt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) IndexSettingsGt(indexSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("index_settings > ?", indexSettings))
}

// IndexSettingsGte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) IndexSettingsGte(indexSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("index_settings >= ?", indexSettings))
}

// IndexSettingsIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) IndexSettingsIn(indexSettings ...string) ESStorageQuerySet {
	if len(indexSettings) == 0 {
		qs.db.AddError(errors.New("must at least pass one indexSettings in IndexSettingsIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("index_settings IN (?)", indexSettings))
}

// IndexSettingsLike is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) IndexSettingsLike(indexSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("index_settings LIKE ?", indexSettings))
}

// IndexSettingsLt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) IndexSettingsLt(indexSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("index_settings < ?", indexSettings))
}

// IndexSettingsLte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) IndexSettingsLte(indexSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("index_settings <= ?", indexSettings))
}

// IndexSettingsNe is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) IndexSettingsNe(indexSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("index_settings != ?", indexSettings))
}

// IndexSettingsNotIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) IndexSettingsNotIn(indexSettings ...string) ESStorageQuerySet {
	if len(indexSettings) == 0 {
		qs.db.AddError(errors.New("must at least pass one indexSettings in IndexSettingsNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("index_settings NOT IN (?)", indexSettings))
}

// IndexSettingsNotlike is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) IndexSettingsNotlike(indexSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("index_settings NOT LIKE ?", indexSettings))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) Limit(limit int) ESStorageQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// MappingSettingsEq is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) MappingSettingsEq(mappingSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("mapping_settings = ?", mappingSettings))
}

// MappingSettingsGt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) MappingSettingsGt(mappingSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("mapping_settings > ?", mappingSettings))
}

// MappingSettingsGte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) MappingSettingsGte(mappingSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("mapping_settings >= ?", mappingSettings))
}

// MappingSettingsIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) MappingSettingsIn(mappingSettings ...string) ESStorageQuerySet {
	if len(mappingSettings) == 0 {
		qs.db.AddError(errors.New("must at least pass one mappingSettings in MappingSettingsIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("mapping_settings IN (?)", mappingSettings))
}

// MappingSettingsLike is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) MappingSettingsLike(mappingSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("mapping_settings LIKE ?", mappingSettings))
}

// MappingSettingsLt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) MappingSettingsLt(mappingSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("mapping_settings < ?", mappingSettings))
}

// MappingSettingsLte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) MappingSettingsLte(mappingSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("mapping_settings <= ?", mappingSettings))
}

// MappingSettingsNe is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) MappingSettingsNe(mappingSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("mapping_settings != ?", mappingSettings))
}

// MappingSettingsNotIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) MappingSettingsNotIn(mappingSettings ...string) ESStorageQuerySet {
	if len(mappingSettings) == 0 {
		qs.db.AddError(errors.New("must at least pass one mappingSettings in MappingSettingsNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("mapping_settings NOT IN (?)", mappingSettings))
}

// MappingSettingsNotlike is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) MappingSettingsNotlike(mappingSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("mapping_settings NOT LIKE ?", mappingSettings))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) Offset(offset int) ESStorageQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs ESStorageQuerySet) One(ret *ESStorage) error {
	return qs.db.First(ret).Error
}

// OrderAscByDateFormat is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderAscByDateFormat() ESStorageQuerySet {
	return qs.w(qs.db.Order("date_format ASC"))
}

// OrderAscByIndexSettings is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderAscByIndexSettings() ESStorageQuerySet {
	return qs.w(qs.db.Order("index_settings ASC"))
}

// OrderAscByMappingSettings is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderAscByMappingSettings() ESStorageQuerySet {
	return qs.w(qs.db.Order("mapping_settings ASC"))
}

// OrderAscByRetention is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderAscByRetention() ESStorageQuerySet {
	return qs.w(qs.db.Order("retention ASC"))
}

// OrderAscBySliceGap is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderAscBySliceGap() ESStorageQuerySet {
	return qs.w(qs.db.Order("slice_gap ASC"))
}

// OrderAscBySliceSize is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderAscBySliceSize() ESStorageQuerySet {
	return qs.w(qs.db.Order("slice_size ASC"))
}

// OrderAscByStorageClusterID is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderAscByStorageClusterID() ESStorageQuerySet {
	return qs.w(qs.db.Order("storage_cluster_id ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderAscByTableID() ESStorageQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderAscByTimeZone is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderAscByTimeZone() ESStorageQuerySet {
	return qs.w(qs.db.Order("time_zone ASC"))
}

// OrderAscByWarmPhaseDays is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderAscByWarmPhaseDays() ESStorageQuerySet {
	return qs.w(qs.db.Order("warm_phase_days ASC"))
}

// OrderAscByWarmPhaseSettings is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderAscByWarmPhaseSettings() ESStorageQuerySet {
	return qs.w(qs.db.Order("warm_phase_settings ASC"))
}

// OrderDescByDateFormat is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderDescByDateFormat() ESStorageQuerySet {
	return qs.w(qs.db.Order("date_format DESC"))
}

// OrderDescByIndexSettings is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderDescByIndexSettings() ESStorageQuerySet {
	return qs.w(qs.db.Order("index_settings DESC"))
}

// OrderDescByMappingSettings is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderDescByMappingSettings() ESStorageQuerySet {
	return qs.w(qs.db.Order("mapping_settings DESC"))
}

// OrderDescByRetention is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderDescByRetention() ESStorageQuerySet {
	return qs.w(qs.db.Order("retention DESC"))
}

// OrderDescBySliceGap is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderDescBySliceGap() ESStorageQuerySet {
	return qs.w(qs.db.Order("slice_gap DESC"))
}

// OrderDescBySliceSize is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderDescBySliceSize() ESStorageQuerySet {
	return qs.w(qs.db.Order("slice_size DESC"))
}

// OrderDescByStorageClusterID is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderDescByStorageClusterID() ESStorageQuerySet {
	return qs.w(qs.db.Order("storage_cluster_id DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderDescByTableID() ESStorageQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// OrderDescByTimeZone is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderDescByTimeZone() ESStorageQuerySet {
	return qs.w(qs.db.Order("time_zone DESC"))
}

// OrderDescByWarmPhaseDays is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderDescByWarmPhaseDays() ESStorageQuerySet {
	return qs.w(qs.db.Order("warm_phase_days DESC"))
}

// OrderDescByWarmPhaseSettings is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) OrderDescByWarmPhaseSettings() ESStorageQuerySet {
	return qs.w(qs.db.Order("warm_phase_settings DESC"))
}

// RetentionEq is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) RetentionEq(retention uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("retention = ?", retention))
}

// RetentionGt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) RetentionGt(retention uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("retention > ?", retention))
}

// RetentionGte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) RetentionGte(retention uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("retention >= ?", retention))
}

// RetentionIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) RetentionIn(retention ...uint) ESStorageQuerySet {
	if len(retention) == 0 {
		qs.db.AddError(errors.New("must at least pass one retention in RetentionIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("retention IN (?)", retention))
}

// RetentionLt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) RetentionLt(retention uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("retention < ?", retention))
}

// RetentionLte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) RetentionLte(retention uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("retention <= ?", retention))
}

// RetentionNe is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) RetentionNe(retention uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("retention != ?", retention))
}

// RetentionNotIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) RetentionNotIn(retention ...uint) ESStorageQuerySet {
	if len(retention) == 0 {
		qs.db.AddError(errors.New("must at least pass one retention in RetentionNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("retention NOT IN (?)", retention))
}

// SliceGapEq is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceGapEq(sliceGap uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("slice_gap = ?", sliceGap))
}

// SliceGapGt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceGapGt(sliceGap uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("slice_gap > ?", sliceGap))
}

// SliceGapGte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceGapGte(sliceGap uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("slice_gap >= ?", sliceGap))
}

// SliceGapIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceGapIn(sliceGap ...uint) ESStorageQuerySet {
	if len(sliceGap) == 0 {
		qs.db.AddError(errors.New("must at least pass one sliceGap in SliceGapIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("slice_gap IN (?)", sliceGap))
}

// SliceGapLt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceGapLt(sliceGap uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("slice_gap < ?", sliceGap))
}

// SliceGapLte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceGapLte(sliceGap uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("slice_gap <= ?", sliceGap))
}

// SliceGapNe is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceGapNe(sliceGap uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("slice_gap != ?", sliceGap))
}

// SliceGapNotIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceGapNotIn(sliceGap ...uint) ESStorageQuerySet {
	if len(sliceGap) == 0 {
		qs.db.AddError(errors.New("must at least pass one sliceGap in SliceGapNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("slice_gap NOT IN (?)", sliceGap))
}

// SliceSizeEq is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceSizeEq(sliceSize uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("slice_size = ?", sliceSize))
}

// SliceSizeGt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceSizeGt(sliceSize uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("slice_size > ?", sliceSize))
}

// SliceSizeGte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceSizeGte(sliceSize uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("slice_size >= ?", sliceSize))
}

// SliceSizeIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceSizeIn(sliceSize ...uint) ESStorageQuerySet {
	if len(sliceSize) == 0 {
		qs.db.AddError(errors.New("must at least pass one sliceSize in SliceSizeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("slice_size IN (?)", sliceSize))
}

// SliceSizeLt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceSizeLt(sliceSize uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("slice_size < ?", sliceSize))
}

// SliceSizeLte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceSizeLte(sliceSize uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("slice_size <= ?", sliceSize))
}

// SliceSizeNe is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceSizeNe(sliceSize uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("slice_size != ?", sliceSize))
}

// SliceSizeNotIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) SliceSizeNotIn(sliceSize ...uint) ESStorageQuerySet {
	if len(sliceSize) == 0 {
		qs.db.AddError(errors.New("must at least pass one sliceSize in SliceSizeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("slice_size NOT IN (?)", sliceSize))
}

// StorageClusterIDEq is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) StorageClusterIDEq(storageClusterID uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id = ?", storageClusterID))
}

// StorageClusterIDGt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) StorageClusterIDGt(storageClusterID uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id > ?", storageClusterID))
}

// StorageClusterIDGte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) StorageClusterIDGte(storageClusterID uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id >= ?", storageClusterID))
}

// StorageClusterIDIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) StorageClusterIDIn(storageClusterID ...uint) ESStorageQuerySet {
	if len(storageClusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one storageClusterID in StorageClusterIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("storage_cluster_id IN (?)", storageClusterID))
}

// StorageClusterIDLt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) StorageClusterIDLt(storageClusterID uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id < ?", storageClusterID))
}

// StorageClusterIDLte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) StorageClusterIDLte(storageClusterID uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id <= ?", storageClusterID))
}

// StorageClusterIDNe is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) StorageClusterIDNe(storageClusterID uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id != ?", storageClusterID))
}

// StorageClusterIDNotIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) StorageClusterIDNotIn(storageClusterID ...uint) ESStorageQuerySet {
	if len(storageClusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one storageClusterID in StorageClusterIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("storage_cluster_id NOT IN (?)", storageClusterID))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TableIDEq(tableID string) ESStorageQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TableIDGt(tableID string) ESStorageQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TableIDGte(tableID string) ESStorageQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TableIDIn(tableID ...string) ESStorageQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TableIDLike(tableID string) ESStorageQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TableIDLt(tableID string) ESStorageQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TableIDLte(tableID string) ESStorageQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TableIDNe(tableID string) ESStorageQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TableIDNotIn(tableID ...string) ESStorageQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TableIDNotlike(tableID string) ESStorageQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// TimeZoneEq is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TimeZoneEq(timeZone int8) ESStorageQuerySet {
	return qs.w(qs.db.Where("time_zone = ?", timeZone))
}

// TimeZoneGt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TimeZoneGt(timeZone int8) ESStorageQuerySet {
	return qs.w(qs.db.Where("time_zone > ?", timeZone))
}

// TimeZoneGte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TimeZoneGte(timeZone int8) ESStorageQuerySet {
	return qs.w(qs.db.Where("time_zone >= ?", timeZone))
}

// TimeZoneIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TimeZoneIn(timeZone ...int8) ESStorageQuerySet {
	if len(timeZone) == 0 {
		qs.db.AddError(errors.New("must at least pass one timeZone in TimeZoneIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("time_zone IN (?)", timeZone))
}

// TimeZoneLt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TimeZoneLt(timeZone int8) ESStorageQuerySet {
	return qs.w(qs.db.Where("time_zone < ?", timeZone))
}

// TimeZoneLte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TimeZoneLte(timeZone int8) ESStorageQuerySet {
	return qs.w(qs.db.Where("time_zone <= ?", timeZone))
}

// TimeZoneNe is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TimeZoneNe(timeZone int8) ESStorageQuerySet {
	return qs.w(qs.db.Where("time_zone != ?", timeZone))
}

// TimeZoneNotIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) TimeZoneNotIn(timeZone ...int8) ESStorageQuerySet {
	if len(timeZone) == 0 {
		qs.db.AddError(errors.New("must at least pass one timeZone in TimeZoneNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("time_zone NOT IN (?)", timeZone))
}

// WarmPhaseDaysEq is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseDaysEq(warmPhaseDays uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_days = ?", warmPhaseDays))
}

// WarmPhaseDaysGt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseDaysGt(warmPhaseDays uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_days > ?", warmPhaseDays))
}

// WarmPhaseDaysGte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseDaysGte(warmPhaseDays uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_days >= ?", warmPhaseDays))
}

// WarmPhaseDaysIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseDaysIn(warmPhaseDays ...uint) ESStorageQuerySet {
	if len(warmPhaseDays) == 0 {
		qs.db.AddError(errors.New("must at least pass one warmPhaseDays in WarmPhaseDaysIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("warm_phase_days IN (?)", warmPhaseDays))
}

// WarmPhaseDaysLt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseDaysLt(warmPhaseDays uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_days < ?", warmPhaseDays))
}

// WarmPhaseDaysLte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseDaysLte(warmPhaseDays uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_days <= ?", warmPhaseDays))
}

// WarmPhaseDaysNe is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseDaysNe(warmPhaseDays uint) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_days != ?", warmPhaseDays))
}

// WarmPhaseDaysNotIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseDaysNotIn(warmPhaseDays ...uint) ESStorageQuerySet {
	if len(warmPhaseDays) == 0 {
		qs.db.AddError(errors.New("must at least pass one warmPhaseDays in WarmPhaseDaysNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("warm_phase_days NOT IN (?)", warmPhaseDays))
}

// WarmPhaseSettingsEq is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseSettingsEq(warmPhaseSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_settings = ?", warmPhaseSettings))
}

// WarmPhaseSettingsGt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseSettingsGt(warmPhaseSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_settings > ?", warmPhaseSettings))
}

// WarmPhaseSettingsGte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseSettingsGte(warmPhaseSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_settings >= ?", warmPhaseSettings))
}

// WarmPhaseSettingsIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseSettingsIn(warmPhaseSettings ...string) ESStorageQuerySet {
	if len(warmPhaseSettings) == 0 {
		qs.db.AddError(errors.New("must at least pass one warmPhaseSettings in WarmPhaseSettingsIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("warm_phase_settings IN (?)", warmPhaseSettings))
}

// WarmPhaseSettingsLike is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseSettingsLike(warmPhaseSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_settings LIKE ?", warmPhaseSettings))
}

// WarmPhaseSettingsLt is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseSettingsLt(warmPhaseSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_settings < ?", warmPhaseSettings))
}

// WarmPhaseSettingsLte is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseSettingsLte(warmPhaseSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_settings <= ?", warmPhaseSettings))
}

// WarmPhaseSettingsNe is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseSettingsNe(warmPhaseSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_settings != ?", warmPhaseSettings))
}

// WarmPhaseSettingsNotIn is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseSettingsNotIn(warmPhaseSettings ...string) ESStorageQuerySet {
	if len(warmPhaseSettings) == 0 {
		qs.db.AddError(errors.New("must at least pass one warmPhaseSettings in WarmPhaseSettingsNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("warm_phase_settings NOT IN (?)", warmPhaseSettings))
}

// WarmPhaseSettingsNotlike is an autogenerated method
// nolint: dupl
func (qs ESStorageQuerySet) WarmPhaseSettingsNotlike(warmPhaseSettings string) ESStorageQuerySet {
	return qs.w(qs.db.Where("warm_phase_settings NOT LIKE ?", warmPhaseSettings))
}

// SetDateFormat is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) SetDateFormat(dateFormat string) ESStorageUpdater {
	u.fields[string(ESStorageDBSchema.DateFormat)] = dateFormat
	return u
}

// SetIndexSettings is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) SetIndexSettings(indexSettings string) ESStorageUpdater {
	u.fields[string(ESStorageDBSchema.IndexSettings)] = indexSettings
	return u
}

// SetMappingSettings is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) SetMappingSettings(mappingSettings string) ESStorageUpdater {
	u.fields[string(ESStorageDBSchema.MappingSettings)] = mappingSettings
	return u
}

// SetRetention is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) SetRetention(retention uint) ESStorageUpdater {
	u.fields[string(ESStorageDBSchema.Retention)] = retention
	return u
}

// SetSliceGap is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) SetSliceGap(sliceGap uint) ESStorageUpdater {
	u.fields[string(ESStorageDBSchema.SliceGap)] = sliceGap
	return u
}

// SetSliceSize is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) SetSliceSize(sliceSize uint) ESStorageUpdater {
	u.fields[string(ESStorageDBSchema.SliceSize)] = sliceSize
	return u
}

// SetStorageClusterID is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) SetStorageClusterID(storageClusterID uint) ESStorageUpdater {
	u.fields[string(ESStorageDBSchema.StorageClusterID)] = storageClusterID
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) SetTableID(tableID string) ESStorageUpdater {
	u.fields[string(ESStorageDBSchema.TableID)] = tableID
	return u
}

// SetTimeZone is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) SetTimeZone(timeZone int8) ESStorageUpdater {
	u.fields[string(ESStorageDBSchema.TimeZone)] = timeZone
	return u
}

// SetWarmPhaseDays is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) SetWarmPhaseDays(warmPhaseDays uint) ESStorageUpdater {
	u.fields[string(ESStorageDBSchema.WarmPhaseDays)] = warmPhaseDays
	return u
}

// SetWarmPhaseSettings is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) SetWarmPhaseSettings(warmPhaseSettings string) ESStorageUpdater {
	u.fields[string(ESStorageDBSchema.WarmPhaseSettings)] = warmPhaseSettings
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u ESStorageUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set ESStorageQuerySet

// ===== BEGIN of ESStorage modifiers

// ESStorageDBSchemaField describes database schema field. It requires for method 'Update'
type ESStorageDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f ESStorageDBSchemaField) String() string {
	return string(f)
}

// ESStorageDBSchema stores db field names of ESStorage
var ESStorageDBSchema = struct {
	TableID           ESStorageDBSchemaField
	DateFormat        ESStorageDBSchemaField
	SliceSize         ESStorageDBSchemaField
	SliceGap          ESStorageDBSchemaField
	Retention         ESStorageDBSchemaField
	WarmPhaseDays     ESStorageDBSchemaField
	WarmPhaseSettings ESStorageDBSchemaField
	TimeZone          ESStorageDBSchemaField
	IndexSettings     ESStorageDBSchemaField
	MappingSettings   ESStorageDBSchemaField
	StorageClusterID  ESStorageDBSchemaField
}{

	TableID:           ESStorageDBSchemaField("table_id"),
	DateFormat:        ESStorageDBSchemaField("date_format"),
	SliceSize:         ESStorageDBSchemaField("slice_size"),
	SliceGap:          ESStorageDBSchemaField("slice_gap"),
	Retention:         ESStorageDBSchemaField("retention"),
	WarmPhaseDays:     ESStorageDBSchemaField("warm_phase_days"),
	WarmPhaseSettings: ESStorageDBSchemaField("warm_phase_settings"),
	TimeZone:          ESStorageDBSchemaField("time_zone"),
	IndexSettings:     ESStorageDBSchemaField("index_settings"),
	MappingSettings:   ESStorageDBSchemaField("mapping_settings"),
	StorageClusterID:  ESStorageDBSchemaField("storage_cluster_id"),
}

// Update updates ESStorage fields by primary key
// nolint: dupl
func (o *ESStorage) Update(db *gorm.DB, fields ...ESStorageDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"table_id":            o.TableID,
		"date_format":         o.DateFormat,
		"slice_size":          o.SliceSize,
		"slice_gap":           o.SliceGap,
		"retention":           o.Retention,
		"warm_phase_days":     o.WarmPhaseDays,
		"warm_phase_settings": o.WarmPhaseSettings,
		"time_zone":           o.TimeZone,
		"index_settings":      o.IndexSettings,
		"mapping_settings":    o.MappingSettings,
		"storage_cluster_id":  o.StorageClusterID,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update ESStorage %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// ESStorageUpdater is an ESStorage updates manager
type ESStorageUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewESStorageUpdater creates new ESStorage updater
// nolint: dupl
func NewESStorageUpdater(db *gorm.DB) ESStorageUpdater {
	return ESStorageUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&ESStorage{}),
	}
}

// ===== END of ESStorage modifiers

// ===== END of all query sets
