// Code generated by go-queryset. DO NOT EDIT.
package storage

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set InfluxdbStorageQuerySet

// InfluxdbStorageQuerySet is an queryset type for InfluxdbStorage
type InfluxdbStorageQuerySet struct {
	db *gorm.DB
}

// NewInfluxdbStorageQuerySet constructs new InfluxdbStorageQuerySet
func NewInfluxdbStorageQuerySet(db *gorm.DB) InfluxdbStorageQuerySet {
	return InfluxdbStorageQuerySet{
		db: db.Model(&InfluxdbStorage{}),
	}
}

func (qs InfluxdbStorageQuerySet) w(db *gorm.DB) InfluxdbStorageQuerySet {
	return NewInfluxdbStorageQuerySet(db)
}

func (qs InfluxdbStorageQuerySet) Select(fields ...InfluxdbStorageDBSchemaField) InfluxdbStorageQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *InfluxdbStorage) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *InfluxdbStorage) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) All(ret *[]InfluxdbStorage) error {
	return qs.db.Find(ret).Error
}

// Count is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// DatabaseEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DatabaseEq(database string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("database = ?", database))
}

// DatabaseGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DatabaseGt(database string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("database > ?", database))
}

// DatabaseGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DatabaseGte(database string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("database >= ?", database))
}

// DatabaseIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DatabaseIn(database ...string) InfluxdbStorageQuerySet {
	if len(database) == 0 {
		qs.db.AddError(errors.New("must at least pass one database in DatabaseIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("database IN (?)", database))
}

// DatabaseLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DatabaseLike(database string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("database LIKE ?", database))
}

// DatabaseLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DatabaseLt(database string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("database < ?", database))
}

// DatabaseLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DatabaseLte(database string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("database <= ?", database))
}

// DatabaseNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DatabaseNe(database string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("database != ?", database))
}

// DatabaseNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DatabaseNotIn(database ...string) InfluxdbStorageQuerySet {
	if len(database) == 0 {
		qs.db.AddError(errors.New("must at least pass one database in DatabaseNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("database NOT IN (?)", database))
}

// DatabaseNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DatabaseNotlike(database string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("database NOT LIKE ?", database))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) Delete() error {
	return qs.db.Delete(InfluxdbStorage{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(InfluxdbStorage{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(InfluxdbStorage{})
	return db.RowsAffected, db.Error
}

// DownSampleDurationTimeEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleDurationTimeEq(downSampleDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_duration_time = ?", downSampleDurationTime))
}

// DownSampleDurationTimeGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleDurationTimeGt(downSampleDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_duration_time > ?", downSampleDurationTime))
}

// DownSampleDurationTimeGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleDurationTimeGte(downSampleDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_duration_time >= ?", downSampleDurationTime))
}

// DownSampleDurationTimeIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleDurationTimeIn(downSampleDurationTime ...string) InfluxdbStorageQuerySet {
	if len(downSampleDurationTime) == 0 {
		qs.db.AddError(errors.New("must at least pass one downSampleDurationTime in DownSampleDurationTimeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("down_sample_duration_time IN (?)", downSampleDurationTime))
}

// DownSampleDurationTimeLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleDurationTimeLike(downSampleDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_duration_time LIKE ?", downSampleDurationTime))
}

// DownSampleDurationTimeLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleDurationTimeLt(downSampleDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_duration_time < ?", downSampleDurationTime))
}

// DownSampleDurationTimeLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleDurationTimeLte(downSampleDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_duration_time <= ?", downSampleDurationTime))
}

// DownSampleDurationTimeNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleDurationTimeNe(downSampleDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_duration_time != ?", downSampleDurationTime))
}

// DownSampleDurationTimeNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleDurationTimeNotIn(downSampleDurationTime ...string) InfluxdbStorageQuerySet {
	if len(downSampleDurationTime) == 0 {
		qs.db.AddError(errors.New("must at least pass one downSampleDurationTime in DownSampleDurationTimeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("down_sample_duration_time NOT IN (?)", downSampleDurationTime))
}

// DownSampleDurationTimeNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleDurationTimeNotlike(downSampleDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_duration_time NOT LIKE ?", downSampleDurationTime))
}

// DownSampleGapEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleGapEq(downSampleGap string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_gap = ?", downSampleGap))
}

// DownSampleGapGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleGapGt(downSampleGap string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_gap > ?", downSampleGap))
}

// DownSampleGapGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleGapGte(downSampleGap string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_gap >= ?", downSampleGap))
}

// DownSampleGapIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleGapIn(downSampleGap ...string) InfluxdbStorageQuerySet {
	if len(downSampleGap) == 0 {
		qs.db.AddError(errors.New("must at least pass one downSampleGap in DownSampleGapIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("down_sample_gap IN (?)", downSampleGap))
}

// DownSampleGapLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleGapLike(downSampleGap string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_gap LIKE ?", downSampleGap))
}

// DownSampleGapLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleGapLt(downSampleGap string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_gap < ?", downSampleGap))
}

// DownSampleGapLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleGapLte(downSampleGap string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_gap <= ?", downSampleGap))
}

// DownSampleGapNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleGapNe(downSampleGap string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_gap != ?", downSampleGap))
}

// DownSampleGapNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleGapNotIn(downSampleGap ...string) InfluxdbStorageQuerySet {
	if len(downSampleGap) == 0 {
		qs.db.AddError(errors.New("must at least pass one downSampleGap in DownSampleGapNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("down_sample_gap NOT IN (?)", downSampleGap))
}

// DownSampleGapNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleGapNotlike(downSampleGap string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_gap NOT LIKE ?", downSampleGap))
}

// DownSampleTableEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleTableEq(downSampleTable string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_table = ?", downSampleTable))
}

// DownSampleTableGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleTableGt(downSampleTable string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_table > ?", downSampleTable))
}

// DownSampleTableGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleTableGte(downSampleTable string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_table >= ?", downSampleTable))
}

// DownSampleTableIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleTableIn(downSampleTable ...string) InfluxdbStorageQuerySet {
	if len(downSampleTable) == 0 {
		qs.db.AddError(errors.New("must at least pass one downSampleTable in DownSampleTableIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("down_sample_table IN (?)", downSampleTable))
}

// DownSampleTableLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleTableLike(downSampleTable string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_table LIKE ?", downSampleTable))
}

// DownSampleTableLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleTableLt(downSampleTable string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_table < ?", downSampleTable))
}

// DownSampleTableLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleTableLte(downSampleTable string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_table <= ?", downSampleTable))
}

// DownSampleTableNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleTableNe(downSampleTable string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_table != ?", downSampleTable))
}

// DownSampleTableNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleTableNotIn(downSampleTable ...string) InfluxdbStorageQuerySet {
	if len(downSampleTable) == 0 {
		qs.db.AddError(errors.New("must at least pass one downSampleTable in DownSampleTableNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("down_sample_table NOT IN (?)", downSampleTable))
}

// DownSampleTableNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) DownSampleTableNotlike(downSampleTable string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("down_sample_table NOT LIKE ?", downSampleTable))
}

// EnableRefreshRpEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) EnableRefreshRpEq(enableRefreshRp bool) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("enable_refresh_rp = ?", enableRefreshRp))
}

// EnableRefreshRpIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) EnableRefreshRpIn(enableRefreshRp ...bool) InfluxdbStorageQuerySet {
	if len(enableRefreshRp) == 0 {
		qs.db.AddError(errors.New("must at least pass one enableRefreshRp in EnableRefreshRpIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("enable_refresh_rp IN (?)", enableRefreshRp))
}

// EnableRefreshRpNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) EnableRefreshRpNe(enableRefreshRp bool) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("enable_refresh_rp != ?", enableRefreshRp))
}

// EnableRefreshRpNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) EnableRefreshRpNotIn(enableRefreshRp ...bool) InfluxdbStorageQuerySet {
	if len(enableRefreshRp) == 0 {
		qs.db.AddError(errors.New("must at least pass one enableRefreshRp in EnableRefreshRpNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("enable_refresh_rp NOT IN (?)", enableRefreshRp))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) GetUpdater() InfluxdbStorageUpdater {
	return NewInfluxdbStorageUpdater(qs.db)
}

// InfluxdbProxyStorageIdEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) InfluxdbProxyStorageIdEq(influxdbProxyStorageId uint) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("influxdb_proxy_storage_id = ?", influxdbProxyStorageId))
}

// InfluxdbProxyStorageIdGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) InfluxdbProxyStorageIdGt(influxdbProxyStorageId uint) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("influxdb_proxy_storage_id > ?", influxdbProxyStorageId))
}

// InfluxdbProxyStorageIdGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) InfluxdbProxyStorageIdGte(influxdbProxyStorageId uint) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("influxdb_proxy_storage_id >= ?", influxdbProxyStorageId))
}

// InfluxdbProxyStorageIdIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) InfluxdbProxyStorageIdIn(influxdbProxyStorageId ...uint) InfluxdbStorageQuerySet {
	if len(influxdbProxyStorageId) == 0 {
		qs.db.AddError(errors.New("must at least pass one influxdbProxyStorageId in InfluxdbProxyStorageIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("influxdb_proxy_storage_id IN (?)", influxdbProxyStorageId))
}

// InfluxdbProxyStorageIdLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) InfluxdbProxyStorageIdLt(influxdbProxyStorageId uint) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("influxdb_proxy_storage_id < ?", influxdbProxyStorageId))
}

// InfluxdbProxyStorageIdLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) InfluxdbProxyStorageIdLte(influxdbProxyStorageId uint) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("influxdb_proxy_storage_id <= ?", influxdbProxyStorageId))
}

// InfluxdbProxyStorageIdNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) InfluxdbProxyStorageIdNe(influxdbProxyStorageId uint) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("influxdb_proxy_storage_id != ?", influxdbProxyStorageId))
}

// InfluxdbProxyStorageIdNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) InfluxdbProxyStorageIdNotIn(influxdbProxyStorageId ...uint) InfluxdbStorageQuerySet {
	if len(influxdbProxyStorageId) == 0 {
		qs.db.AddError(errors.New("must at least pass one influxdbProxyStorageId in InfluxdbProxyStorageIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("influxdb_proxy_storage_id NOT IN (?)", influxdbProxyStorageId))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) Limit(limit int) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) Offset(offset int) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs InfluxdbStorageQuerySet) One(ret *InfluxdbStorage) error {
	return qs.db.First(ret).Error
}

// OrderAscByDatabase is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByDatabase() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("database ASC"))
}

// OrderAscByDownSampleDurationTime is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByDownSampleDurationTime() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("down_sample_duration_time ASC"))
}

// OrderAscByDownSampleGap is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByDownSampleGap() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("down_sample_gap ASC"))
}

// OrderAscByDownSampleTable is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByDownSampleTable() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("down_sample_table ASC"))
}

// OrderAscByEnableRefreshRp is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByEnableRefreshRp() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("enable_refresh_rp ASC"))
}

// OrderAscByInfluxdbProxyStorageId is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByInfluxdbProxyStorageId() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("influxdb_proxy_storage_id ASC"))
}

// OrderAscByPartitionTag is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByPartitionTag() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("partition_tag ASC"))
}

// OrderAscByProxyClusterName is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByProxyClusterName() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("proxy_cluster_name ASC"))
}

// OrderAscByRealTableName is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByRealTableName() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("real_table_name ASC"))
}

// OrderAscBySourceDurationTime is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscBySourceDurationTime() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("source_duration_time ASC"))
}

// OrderAscByStorageClusterID is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByStorageClusterID() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("storage_cluster_id ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByTableID() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderAscByUseDefaultRp is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByUseDefaultRp() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("use_default_rp ASC"))
}

// OrderAscByVmTableId is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderAscByVmTableId() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("vm_table_id ASC"))
}

// OrderDescByDatabase is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByDatabase() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("database DESC"))
}

// OrderDescByDownSampleDurationTime is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByDownSampleDurationTime() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("down_sample_duration_time DESC"))
}

// OrderDescByDownSampleGap is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByDownSampleGap() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("down_sample_gap DESC"))
}

// OrderDescByDownSampleTable is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByDownSampleTable() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("down_sample_table DESC"))
}

// OrderDescByEnableRefreshRp is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByEnableRefreshRp() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("enable_refresh_rp DESC"))
}

// OrderDescByInfluxdbProxyStorageId is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByInfluxdbProxyStorageId() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("influxdb_proxy_storage_id DESC"))
}

// OrderDescByPartitionTag is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByPartitionTag() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("partition_tag DESC"))
}

// OrderDescByProxyClusterName is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByProxyClusterName() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("proxy_cluster_name DESC"))
}

// OrderDescByRealTableName is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByRealTableName() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("real_table_name DESC"))
}

// OrderDescBySourceDurationTime is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescBySourceDurationTime() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("source_duration_time DESC"))
}

// OrderDescByStorageClusterID is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByStorageClusterID() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("storage_cluster_id DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByTableID() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// OrderDescByUseDefaultRp is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByUseDefaultRp() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("use_default_rp DESC"))
}

// OrderDescByVmTableId is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) OrderDescByVmTableId() InfluxdbStorageQuerySet {
	return qs.w(qs.db.Order("vm_table_id DESC"))
}

// PartitionTagEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) PartitionTagEq(partitionTag string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("partition_tag = ?", partitionTag))
}

// PartitionTagGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) PartitionTagGt(partitionTag string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("partition_tag > ?", partitionTag))
}

// PartitionTagGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) PartitionTagGte(partitionTag string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("partition_tag >= ?", partitionTag))
}

// PartitionTagIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) PartitionTagIn(partitionTag ...string) InfluxdbStorageQuerySet {
	if len(partitionTag) == 0 {
		qs.db.AddError(errors.New("must at least pass one partitionTag in PartitionTagIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("partition_tag IN (?)", partitionTag))
}

// PartitionTagLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) PartitionTagLike(partitionTag string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("partition_tag LIKE ?", partitionTag))
}

// PartitionTagLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) PartitionTagLt(partitionTag string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("partition_tag < ?", partitionTag))
}

// PartitionTagLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) PartitionTagLte(partitionTag string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("partition_tag <= ?", partitionTag))
}

// PartitionTagNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) PartitionTagNe(partitionTag string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("partition_tag != ?", partitionTag))
}

// PartitionTagNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) PartitionTagNotIn(partitionTag ...string) InfluxdbStorageQuerySet {
	if len(partitionTag) == 0 {
		qs.db.AddError(errors.New("must at least pass one partitionTag in PartitionTagNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("partition_tag NOT IN (?)", partitionTag))
}

// PartitionTagNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) PartitionTagNotlike(partitionTag string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("partition_tag NOT LIKE ?", partitionTag))
}

// ProxyClusterNameEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) ProxyClusterNameEq(proxyClusterName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_name = ?", proxyClusterName))
}

// ProxyClusterNameGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) ProxyClusterNameGt(proxyClusterName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_name > ?", proxyClusterName))
}

// ProxyClusterNameGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) ProxyClusterNameGte(proxyClusterName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_name >= ?", proxyClusterName))
}

// ProxyClusterNameIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) ProxyClusterNameIn(proxyClusterName ...string) InfluxdbStorageQuerySet {
	if len(proxyClusterName) == 0 {
		qs.db.AddError(errors.New("must at least pass one proxyClusterName in ProxyClusterNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("proxy_cluster_name IN (?)", proxyClusterName))
}

// ProxyClusterNameLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) ProxyClusterNameLike(proxyClusterName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_name LIKE ?", proxyClusterName))
}

// ProxyClusterNameLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) ProxyClusterNameLt(proxyClusterName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_name < ?", proxyClusterName))
}

// ProxyClusterNameLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) ProxyClusterNameLte(proxyClusterName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_name <= ?", proxyClusterName))
}

// ProxyClusterNameNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) ProxyClusterNameNe(proxyClusterName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_name != ?", proxyClusterName))
}

// ProxyClusterNameNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) ProxyClusterNameNotIn(proxyClusterName ...string) InfluxdbStorageQuerySet {
	if len(proxyClusterName) == 0 {
		qs.db.AddError(errors.New("must at least pass one proxyClusterName in ProxyClusterNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("proxy_cluster_name NOT IN (?)", proxyClusterName))
}

// ProxyClusterNameNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) ProxyClusterNameNotlike(proxyClusterName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_name NOT LIKE ?", proxyClusterName))
}

// RealTableNameEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) RealTableNameEq(realTableName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("real_table_name = ?", realTableName))
}

// RealTableNameGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) RealTableNameGt(realTableName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("real_table_name > ?", realTableName))
}

// RealTableNameGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) RealTableNameGte(realTableName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("real_table_name >= ?", realTableName))
}

// RealTableNameIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) RealTableNameIn(realTableName ...string) InfluxdbStorageQuerySet {
	if len(realTableName) == 0 {
		qs.db.AddError(errors.New("must at least pass one realTableName in RealTableNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("real_table_name IN (?)", realTableName))
}

// RealTableNameLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) RealTableNameLike(realTableName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("real_table_name LIKE ?", realTableName))
}

// RealTableNameLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) RealTableNameLt(realTableName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("real_table_name < ?", realTableName))
}

// RealTableNameLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) RealTableNameLte(realTableName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("real_table_name <= ?", realTableName))
}

// RealTableNameNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) RealTableNameNe(realTableName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("real_table_name != ?", realTableName))
}

// RealTableNameNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) RealTableNameNotIn(realTableName ...string) InfluxdbStorageQuerySet {
	if len(realTableName) == 0 {
		qs.db.AddError(errors.New("must at least pass one realTableName in RealTableNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("real_table_name NOT IN (?)", realTableName))
}

// RealTableNameNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) RealTableNameNotlike(realTableName string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("real_table_name NOT LIKE ?", realTableName))
}

// SourceDurationTimeEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) SourceDurationTimeEq(sourceDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("source_duration_time = ?", sourceDurationTime))
}

// SourceDurationTimeGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) SourceDurationTimeGt(sourceDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("source_duration_time > ?", sourceDurationTime))
}

// SourceDurationTimeGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) SourceDurationTimeGte(sourceDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("source_duration_time >= ?", sourceDurationTime))
}

// SourceDurationTimeIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) SourceDurationTimeIn(sourceDurationTime ...string) InfluxdbStorageQuerySet {
	if len(sourceDurationTime) == 0 {
		qs.db.AddError(errors.New("must at least pass one sourceDurationTime in SourceDurationTimeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("source_duration_time IN (?)", sourceDurationTime))
}

// SourceDurationTimeLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) SourceDurationTimeLike(sourceDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("source_duration_time LIKE ?", sourceDurationTime))
}

// SourceDurationTimeLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) SourceDurationTimeLt(sourceDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("source_duration_time < ?", sourceDurationTime))
}

// SourceDurationTimeLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) SourceDurationTimeLte(sourceDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("source_duration_time <= ?", sourceDurationTime))
}

// SourceDurationTimeNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) SourceDurationTimeNe(sourceDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("source_duration_time != ?", sourceDurationTime))
}

// SourceDurationTimeNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) SourceDurationTimeNotIn(sourceDurationTime ...string) InfluxdbStorageQuerySet {
	if len(sourceDurationTime) == 0 {
		qs.db.AddError(errors.New("must at least pass one sourceDurationTime in SourceDurationTimeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("source_duration_time NOT IN (?)", sourceDurationTime))
}

// SourceDurationTimeNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) SourceDurationTimeNotlike(sourceDurationTime string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("source_duration_time NOT LIKE ?", sourceDurationTime))
}

// StorageClusterIDEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) StorageClusterIDEq(storageClusterID uint) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id = ?", storageClusterID))
}

// StorageClusterIDGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) StorageClusterIDGt(storageClusterID uint) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id > ?", storageClusterID))
}

// StorageClusterIDGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) StorageClusterIDGte(storageClusterID uint) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id >= ?", storageClusterID))
}

// StorageClusterIDIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) StorageClusterIDIn(storageClusterID ...uint) InfluxdbStorageQuerySet {
	if len(storageClusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one storageClusterID in StorageClusterIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("storage_cluster_id IN (?)", storageClusterID))
}

// StorageClusterIDLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) StorageClusterIDLt(storageClusterID uint) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id < ?", storageClusterID))
}

// StorageClusterIDLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) StorageClusterIDLte(storageClusterID uint) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id <= ?", storageClusterID))
}

// StorageClusterIDNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) StorageClusterIDNe(storageClusterID uint) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id != ?", storageClusterID))
}

// StorageClusterIDNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) StorageClusterIDNotIn(storageClusterID ...uint) InfluxdbStorageQuerySet {
	if len(storageClusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one storageClusterID in StorageClusterIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("storage_cluster_id NOT IN (?)", storageClusterID))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) TableIDEq(tableID string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) TableIDGt(tableID string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) TableIDGte(tableID string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) TableIDIn(tableID ...string) InfluxdbStorageQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) TableIDLike(tableID string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) TableIDLt(tableID string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) TableIDLte(tableID string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) TableIDNe(tableID string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) TableIDNotIn(tableID ...string) InfluxdbStorageQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) TableIDNotlike(tableID string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// UseDefaultRpEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) UseDefaultRpEq(useDefaultRp bool) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("use_default_rp = ?", useDefaultRp))
}

// UseDefaultRpIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) UseDefaultRpIn(useDefaultRp ...bool) InfluxdbStorageQuerySet {
	if len(useDefaultRp) == 0 {
		qs.db.AddError(errors.New("must at least pass one useDefaultRp in UseDefaultRpIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("use_default_rp IN (?)", useDefaultRp))
}

// UseDefaultRpNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) UseDefaultRpNe(useDefaultRp bool) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("use_default_rp != ?", useDefaultRp))
}

// UseDefaultRpNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) UseDefaultRpNotIn(useDefaultRp ...bool) InfluxdbStorageQuerySet {
	if len(useDefaultRp) == 0 {
		qs.db.AddError(errors.New("must at least pass one useDefaultRp in UseDefaultRpNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("use_default_rp NOT IN (?)", useDefaultRp))
}

// VmTableIdEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) VmTableIdEq(vmTableId string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("vm_table_id = ?", vmTableId))
}

// VmTableIdGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) VmTableIdGt(vmTableId string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("vm_table_id > ?", vmTableId))
}

// VmTableIdGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) VmTableIdGte(vmTableId string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("vm_table_id >= ?", vmTableId))
}

// VmTableIdIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) VmTableIdIn(vmTableId ...string) InfluxdbStorageQuerySet {
	if len(vmTableId) == 0 {
		qs.db.AddError(errors.New("must at least pass one vmTableId in VmTableIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("vm_table_id IN (?)", vmTableId))
}

// VmTableIdLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) VmTableIdLike(vmTableId string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("vm_table_id LIKE ?", vmTableId))
}

// VmTableIdLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) VmTableIdLt(vmTableId string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("vm_table_id < ?", vmTableId))
}

// VmTableIdLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) VmTableIdLte(vmTableId string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("vm_table_id <= ?", vmTableId))
}

// VmTableIdNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) VmTableIdNe(vmTableId string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("vm_table_id != ?", vmTableId))
}

// VmTableIdNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) VmTableIdNotIn(vmTableId ...string) InfluxdbStorageQuerySet {
	if len(vmTableId) == 0 {
		qs.db.AddError(errors.New("must at least pass one vmTableId in VmTableIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("vm_table_id NOT IN (?)", vmTableId))
}

// VmTableIdNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbStorageQuerySet) VmTableIdNotlike(vmTableId string) InfluxdbStorageQuerySet {
	return qs.w(qs.db.Where("vm_table_id NOT LIKE ?", vmTableId))
}

// SetDatabase is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetDatabase(database string) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.Database)] = database
	return u
}

// SetDownSampleDurationTime is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetDownSampleDurationTime(downSampleDurationTime string) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.DownSampleDurationTime)] = downSampleDurationTime
	return u
}

// SetDownSampleGap is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetDownSampleGap(downSampleGap string) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.DownSampleGap)] = downSampleGap
	return u
}

// SetDownSampleTable is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetDownSampleTable(downSampleTable string) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.DownSampleTable)] = downSampleTable
	return u
}

// SetEnableRefreshRp is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetEnableRefreshRp(enableRefreshRp bool) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.EnableRefreshRp)] = enableRefreshRp
	return u
}

// SetInfluxdbProxyStorageId is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetInfluxdbProxyStorageId(influxdbProxyStorageId uint) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.InfluxdbProxyStorageId)] = influxdbProxyStorageId
	return u
}

// SetPartitionTag is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetPartitionTag(partitionTag string) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.PartitionTag)] = partitionTag
	return u
}

// SetProxyClusterName is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetProxyClusterName(proxyClusterName string) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.ProxyClusterName)] = proxyClusterName
	return u
}

// SetRealTableName is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetRealTableName(realTableName string) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.RealTableName)] = realTableName
	return u
}

// SetSourceDurationTime is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetSourceDurationTime(sourceDurationTime string) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.SourceDurationTime)] = sourceDurationTime
	return u
}

// SetStorageClusterID is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetStorageClusterID(storageClusterID uint) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.StorageClusterID)] = storageClusterID
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetTableID(tableID string) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.TableID)] = tableID
	return u
}

// SetUseDefaultRp is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetUseDefaultRp(useDefaultRp bool) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.UseDefaultRp)] = useDefaultRp
	return u
}

// SetVmTableId is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) SetVmTableId(vmTableId string) InfluxdbStorageUpdater {
	u.fields[string(InfluxdbStorageDBSchema.VmTableId)] = vmTableId
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u InfluxdbStorageUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set InfluxdbStorageQuerySet

// ===== BEGIN of InfluxdbStorage modifiers

// InfluxdbStorageDBSchemaField describes database schema field. It requires for method 'Update'
type InfluxdbStorageDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f InfluxdbStorageDBSchemaField) String() string {
	return string(f)
}

// InfluxdbStorageDBSchema stores db field names of InfluxdbStorage
var InfluxdbStorageDBSchema = struct {
	TableID                InfluxdbStorageDBSchemaField
	StorageClusterID       InfluxdbStorageDBSchemaField
	RealTableName          InfluxdbStorageDBSchemaField
	Database               InfluxdbStorageDBSchemaField
	SourceDurationTime     InfluxdbStorageDBSchemaField
	DownSampleTable        InfluxdbStorageDBSchemaField
	DownSampleGap          InfluxdbStorageDBSchemaField
	DownSampleDurationTime InfluxdbStorageDBSchemaField
	ProxyClusterName       InfluxdbStorageDBSchemaField
	UseDefaultRp           InfluxdbStorageDBSchemaField
	EnableRefreshRp        InfluxdbStorageDBSchemaField
	PartitionTag           InfluxdbStorageDBSchemaField
	VmTableId              InfluxdbStorageDBSchemaField
	InfluxdbProxyStorageId InfluxdbStorageDBSchemaField
}{

	TableID:                InfluxdbStorageDBSchemaField("table_id"),
	StorageClusterID:       InfluxdbStorageDBSchemaField("storage_cluster_id"),
	RealTableName:          InfluxdbStorageDBSchemaField("real_table_name"),
	Database:               InfluxdbStorageDBSchemaField("database"),
	SourceDurationTime:     InfluxdbStorageDBSchemaField("source_duration_time"),
	DownSampleTable:        InfluxdbStorageDBSchemaField("down_sample_table"),
	DownSampleGap:          InfluxdbStorageDBSchemaField("down_sample_gap"),
	DownSampleDurationTime: InfluxdbStorageDBSchemaField("down_sample_duration_time"),
	ProxyClusterName:       InfluxdbStorageDBSchemaField("proxy_cluster_name"),
	UseDefaultRp:           InfluxdbStorageDBSchemaField("use_default_rp"),
	EnableRefreshRp:        InfluxdbStorageDBSchemaField("enable_refresh_rp"),
	PartitionTag:           InfluxdbStorageDBSchemaField("partition_tag"),
	VmTableId:              InfluxdbStorageDBSchemaField("vm_table_id"),
	InfluxdbProxyStorageId: InfluxdbStorageDBSchemaField("influxdb_proxy_storage_id"),
}

// Update updates InfluxdbStorage fields by primary key
// nolint: dupl
func (o *InfluxdbStorage) Update(db *gorm.DB, fields ...InfluxdbStorageDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"table_id":                  o.TableID,
		"storage_cluster_id":        o.StorageClusterID,
		"real_table_name":           o.RealTableName,
		"database":                  o.Database,
		"source_duration_time":      o.SourceDurationTime,
		"down_sample_table":         o.DownSampleTable,
		"down_sample_gap":           o.DownSampleGap,
		"down_sample_duration_time": o.DownSampleDurationTime,
		"proxy_cluster_name":        o.ProxyClusterName,
		"use_default_rp":            o.UseDefaultRp,
		"enable_refresh_rp":         o.EnableRefreshRp,
		"partition_tag":             o.PartitionTag,
		"vm_table_id":               o.VmTableId,
		"influxdb_proxy_storage_id": o.InfluxdbProxyStorageId,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update InfluxdbStorage %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// InfluxdbStorageUpdater is an InfluxdbStorage updates manager
type InfluxdbStorageUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewInfluxdbStorageUpdater creates new InfluxdbStorage updater
// nolint: dupl
func NewInfluxdbStorageUpdater(db *gorm.DB) InfluxdbStorageUpdater {
	return InfluxdbStorageUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&InfluxdbStorage{}),
	}
}

// ===== END of InfluxdbStorage modifiers

// ===== END of all query sets
