// Code generated by go-queryset. DO NOT EDIT.
package storage

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set InfluxdbHostInfoQuerySet

// InfluxdbHostInfoQuerySet is an queryset type for InfluxdbHostInfo
type InfluxdbHostInfoQuerySet struct {
	db *gorm.DB
}

// NewInfluxdbHostInfoQuerySet constructs new InfluxdbHostInfoQuerySet
func NewInfluxdbHostInfoQuerySet(db *gorm.DB) InfluxdbHostInfoQuerySet {
	return InfluxdbHostInfoQuerySet{
		db: db.Model(&InfluxdbHostInfo{}),
	}
}

func (qs InfluxdbHostInfoQuerySet) w(db *gorm.DB) InfluxdbHostInfoQuerySet {
	return NewInfluxdbHostInfoQuerySet(db)
}

func (qs InfluxdbHostInfoQuerySet) Select(fields ...InfluxdbHostInfoDBSchemaField) InfluxdbHostInfoQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *InfluxdbHostInfo) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *InfluxdbHostInfo) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) All(ret *[]InfluxdbHostInfo) error {
	return qs.db.Find(ret).Error
}

// BackupRateLimitEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) BackupRateLimitEq(backupRateLimit float64) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("backup_rate_limit = ?", backupRateLimit))
}

// BackupRateLimitGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) BackupRateLimitGt(backupRateLimit float64) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("backup_rate_limit > ?", backupRateLimit))
}

// BackupRateLimitGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) BackupRateLimitGte(backupRateLimit float64) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("backup_rate_limit >= ?", backupRateLimit))
}

// BackupRateLimitIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) BackupRateLimitIn(backupRateLimit ...float64) InfluxdbHostInfoQuerySet {
	if len(backupRateLimit) == 0 {
		qs.db.AddError(errors.New("must at least pass one backupRateLimit in BackupRateLimitIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("backup_rate_limit IN (?)", backupRateLimit))
}

// BackupRateLimitLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) BackupRateLimitLt(backupRateLimit float64) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("backup_rate_limit < ?", backupRateLimit))
}

// BackupRateLimitLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) BackupRateLimitLte(backupRateLimit float64) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("backup_rate_limit <= ?", backupRateLimit))
}

// BackupRateLimitNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) BackupRateLimitNe(backupRateLimit float64) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("backup_rate_limit != ?", backupRateLimit))
}

// BackupRateLimitNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) BackupRateLimitNotIn(backupRateLimit ...float64) InfluxdbHostInfoQuerySet {
	if len(backupRateLimit) == 0 {
		qs.db.AddError(errors.New("must at least pass one backupRateLimit in BackupRateLimitNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("backup_rate_limit NOT IN (?)", backupRateLimit))
}

// Count is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// Delete is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) Delete() error {
	return qs.db.Delete(InfluxdbHostInfo{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(InfluxdbHostInfo{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(InfluxdbHostInfo{})
	return db.RowsAffected, db.Error
}

// DescriptionEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DescriptionEq(description string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("description = ?", description))
}

// DescriptionGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DescriptionGt(description string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("description > ?", description))
}

// DescriptionGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DescriptionGte(description string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("description >= ?", description))
}

// DescriptionIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DescriptionIn(description ...string) InfluxdbHostInfoQuerySet {
	if len(description) == 0 {
		qs.db.AddError(errors.New("must at least pass one description in DescriptionIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("description IN (?)", description))
}

// DescriptionLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DescriptionLike(description string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("description LIKE ?", description))
}

// DescriptionLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DescriptionLt(description string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("description < ?", description))
}

// DescriptionLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DescriptionLte(description string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("description <= ?", description))
}

// DescriptionNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DescriptionNe(description string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("description != ?", description))
}

// DescriptionNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DescriptionNotIn(description ...string) InfluxdbHostInfoQuerySet {
	if len(description) == 0 {
		qs.db.AddError(errors.New("must at least pass one description in DescriptionNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("description NOT IN (?)", description))
}

// DescriptionNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DescriptionNotlike(description string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("description NOT LIKE ?", description))
}

// DomainNameEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DomainNameEq(domainName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("domain_name = ?", domainName))
}

// DomainNameGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DomainNameGt(domainName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("domain_name > ?", domainName))
}

// DomainNameGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DomainNameGte(domainName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("domain_name >= ?", domainName))
}

// DomainNameIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DomainNameIn(domainName ...string) InfluxdbHostInfoQuerySet {
	if len(domainName) == 0 {
		qs.db.AddError(errors.New("must at least pass one domainName in DomainNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("domain_name IN (?)", domainName))
}

// DomainNameLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DomainNameLike(domainName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("domain_name LIKE ?", domainName))
}

// DomainNameLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DomainNameLt(domainName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("domain_name < ?", domainName))
}

// DomainNameLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DomainNameLte(domainName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("domain_name <= ?", domainName))
}

// DomainNameNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DomainNameNe(domainName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("domain_name != ?", domainName))
}

// DomainNameNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DomainNameNotIn(domainName ...string) InfluxdbHostInfoQuerySet {
	if len(domainName) == 0 {
		qs.db.AddError(errors.New("must at least pass one domainName in DomainNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("domain_name NOT IN (?)", domainName))
}

// DomainNameNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) DomainNameNotlike(domainName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("domain_name NOT LIKE ?", domainName))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) GetUpdater() InfluxdbHostInfoUpdater {
	return NewInfluxdbHostInfoUpdater(qs.db)
}

// HostNameEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) HostNameEq(hostName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("host_name = ?", hostName))
}

// HostNameGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) HostNameGt(hostName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("host_name > ?", hostName))
}

// HostNameGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) HostNameGte(hostName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("host_name >= ?", hostName))
}

// HostNameIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) HostNameIn(hostName ...string) InfluxdbHostInfoQuerySet {
	if len(hostName) == 0 {
		qs.db.AddError(errors.New("must at least pass one hostName in HostNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("host_name IN (?)", hostName))
}

// HostNameLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) HostNameLike(hostName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("host_name LIKE ?", hostName))
}

// HostNameLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) HostNameLt(hostName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("host_name < ?", hostName))
}

// HostNameLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) HostNameLte(hostName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("host_name <= ?", hostName))
}

// HostNameNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) HostNameNe(hostName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("host_name != ?", hostName))
}

// HostNameNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) HostNameNotIn(hostName ...string) InfluxdbHostInfoQuerySet {
	if len(hostName) == 0 {
		qs.db.AddError(errors.New("must at least pass one hostName in HostNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("host_name NOT IN (?)", hostName))
}

// HostNameNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) HostNameNotlike(hostName string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("host_name NOT LIKE ?", hostName))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) Limit(limit int) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) Offset(offset int) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs InfluxdbHostInfoQuerySet) One(ret *InfluxdbHostInfo) error {
	return qs.db.First(ret).Error
}

// OrderAscByBackupRateLimit is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderAscByBackupRateLimit() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("backup_rate_limit ASC"))
}

// OrderAscByDescription is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderAscByDescription() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("description ASC"))
}

// OrderAscByDomainName is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderAscByDomainName() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("domain_name ASC"))
}

// OrderAscByHostName is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderAscByHostName() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("host_name ASC"))
}

// OrderAscByPassword is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderAscByPassword() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("password ASC"))
}

// OrderAscByPort is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderAscByPort() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("port ASC"))
}

// OrderAscByProtocol is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderAscByProtocol() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("protocol ASC"))
}

// OrderAscByReadRateLimit is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderAscByReadRateLimit() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("read_rate_limit ASC"))
}

// OrderAscByStatus is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderAscByStatus() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("status ASC"))
}

// OrderAscByUsername is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderAscByUsername() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("username ASC"))
}

// OrderDescByBackupRateLimit is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderDescByBackupRateLimit() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("backup_rate_limit DESC"))
}

// OrderDescByDescription is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderDescByDescription() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("description DESC"))
}

// OrderDescByDomainName is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderDescByDomainName() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("domain_name DESC"))
}

// OrderDescByHostName is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderDescByHostName() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("host_name DESC"))
}

// OrderDescByPassword is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderDescByPassword() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("password DESC"))
}

// OrderDescByPort is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderDescByPort() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("port DESC"))
}

// OrderDescByProtocol is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderDescByProtocol() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("protocol DESC"))
}

// OrderDescByReadRateLimit is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderDescByReadRateLimit() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("read_rate_limit DESC"))
}

// OrderDescByStatus is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderDescByStatus() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("status DESC"))
}

// OrderDescByUsername is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) OrderDescByUsername() InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Order("username DESC"))
}

// PasswordEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PasswordEq(password string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("password = ?", password))
}

// PasswordGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PasswordGt(password string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("password > ?", password))
}

// PasswordGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PasswordGte(password string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("password >= ?", password))
}

// PasswordIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PasswordIn(password ...string) InfluxdbHostInfoQuerySet {
	if len(password) == 0 {
		qs.db.AddError(errors.New("must at least pass one password in PasswordIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("password IN (?)", password))
}

// PasswordLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PasswordLike(password string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("password LIKE ?", password))
}

// PasswordLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PasswordLt(password string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("password < ?", password))
}

// PasswordLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PasswordLte(password string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("password <= ?", password))
}

// PasswordNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PasswordNe(password string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("password != ?", password))
}

// PasswordNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PasswordNotIn(password ...string) InfluxdbHostInfoQuerySet {
	if len(password) == 0 {
		qs.db.AddError(errors.New("must at least pass one password in PasswordNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("password NOT IN (?)", password))
}

// PasswordNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PasswordNotlike(password string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("password NOT LIKE ?", password))
}

// PortEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PortEq(port uint) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("port = ?", port))
}

// PortGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PortGt(port uint) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("port > ?", port))
}

// PortGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PortGte(port uint) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("port >= ?", port))
}

// PortIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PortIn(port ...uint) InfluxdbHostInfoQuerySet {
	if len(port) == 0 {
		qs.db.AddError(errors.New("must at least pass one port in PortIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("port IN (?)", port))
}

// PortLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PortLt(port uint) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("port < ?", port))
}

// PortLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PortLte(port uint) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("port <= ?", port))
}

// PortNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PortNe(port uint) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("port != ?", port))
}

// PortNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) PortNotIn(port ...uint) InfluxdbHostInfoQuerySet {
	if len(port) == 0 {
		qs.db.AddError(errors.New("must at least pass one port in PortNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("port NOT IN (?)", port))
}

// ProtocolEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ProtocolEq(protocol string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("protocol = ?", protocol))
}

// ProtocolGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ProtocolGt(protocol string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("protocol > ?", protocol))
}

// ProtocolGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ProtocolGte(protocol string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("protocol >= ?", protocol))
}

// ProtocolIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ProtocolIn(protocol ...string) InfluxdbHostInfoQuerySet {
	if len(protocol) == 0 {
		qs.db.AddError(errors.New("must at least pass one protocol in ProtocolIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("protocol IN (?)", protocol))
}

// ProtocolLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ProtocolLike(protocol string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("protocol LIKE ?", protocol))
}

// ProtocolLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ProtocolLt(protocol string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("protocol < ?", protocol))
}

// ProtocolLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ProtocolLte(protocol string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("protocol <= ?", protocol))
}

// ProtocolNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ProtocolNe(protocol string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("protocol != ?", protocol))
}

// ProtocolNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ProtocolNotIn(protocol ...string) InfluxdbHostInfoQuerySet {
	if len(protocol) == 0 {
		qs.db.AddError(errors.New("must at least pass one protocol in ProtocolNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("protocol NOT IN (?)", protocol))
}

// ProtocolNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ProtocolNotlike(protocol string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("protocol NOT LIKE ?", protocol))
}

// ReadRateLimitEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ReadRateLimitEq(readRateLimit float64) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("read_rate_limit = ?", readRateLimit))
}

// ReadRateLimitGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ReadRateLimitGt(readRateLimit float64) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("read_rate_limit > ?", readRateLimit))
}

// ReadRateLimitGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ReadRateLimitGte(readRateLimit float64) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("read_rate_limit >= ?", readRateLimit))
}

// ReadRateLimitIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ReadRateLimitIn(readRateLimit ...float64) InfluxdbHostInfoQuerySet {
	if len(readRateLimit) == 0 {
		qs.db.AddError(errors.New("must at least pass one readRateLimit in ReadRateLimitIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("read_rate_limit IN (?)", readRateLimit))
}

// ReadRateLimitLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ReadRateLimitLt(readRateLimit float64) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("read_rate_limit < ?", readRateLimit))
}

// ReadRateLimitLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ReadRateLimitLte(readRateLimit float64) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("read_rate_limit <= ?", readRateLimit))
}

// ReadRateLimitNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ReadRateLimitNe(readRateLimit float64) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("read_rate_limit != ?", readRateLimit))
}

// ReadRateLimitNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) ReadRateLimitNotIn(readRateLimit ...float64) InfluxdbHostInfoQuerySet {
	if len(readRateLimit) == 0 {
		qs.db.AddError(errors.New("must at least pass one readRateLimit in ReadRateLimitNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("read_rate_limit NOT IN (?)", readRateLimit))
}

// StatusEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) StatusEq(status bool) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("status = ?", status))
}

// StatusIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) StatusIn(status ...bool) InfluxdbHostInfoQuerySet {
	if len(status) == 0 {
		qs.db.AddError(errors.New("must at least pass one status in StatusIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("status IN (?)", status))
}

// StatusNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) StatusNe(status bool) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("status != ?", status))
}

// StatusNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) StatusNotIn(status ...bool) InfluxdbHostInfoQuerySet {
	if len(status) == 0 {
		qs.db.AddError(errors.New("must at least pass one status in StatusNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("status NOT IN (?)", status))
}

// UsernameEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) UsernameEq(username string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("username = ?", username))
}

// UsernameGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) UsernameGt(username string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("username > ?", username))
}

// UsernameGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) UsernameGte(username string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("username >= ?", username))
}

// UsernameIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) UsernameIn(username ...string) InfluxdbHostInfoQuerySet {
	if len(username) == 0 {
		qs.db.AddError(errors.New("must at least pass one username in UsernameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("username IN (?)", username))
}

// UsernameLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) UsernameLike(username string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("username LIKE ?", username))
}

// UsernameLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) UsernameLt(username string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("username < ?", username))
}

// UsernameLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) UsernameLte(username string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("username <= ?", username))
}

// UsernameNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) UsernameNe(username string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("username != ?", username))
}

// UsernameNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) UsernameNotIn(username ...string) InfluxdbHostInfoQuerySet {
	if len(username) == 0 {
		qs.db.AddError(errors.New("must at least pass one username in UsernameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("username NOT IN (?)", username))
}

// UsernameNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbHostInfoQuerySet) UsernameNotlike(username string) InfluxdbHostInfoQuerySet {
	return qs.w(qs.db.Where("username NOT LIKE ?", username))
}

// SetBackupRateLimit is an autogenerated method
// nolint: dupl
func (u InfluxdbHostInfoUpdater) SetBackupRateLimit(backupRateLimit float64) InfluxdbHostInfoUpdater {
	u.fields[string(InfluxdbHostInfoDBSchema.BackupRateLimit)] = backupRateLimit
	return u
}

// SetDescription is an autogenerated method
// nolint: dupl
func (u InfluxdbHostInfoUpdater) SetDescription(description string) InfluxdbHostInfoUpdater {
	u.fields[string(InfluxdbHostInfoDBSchema.Description)] = description
	return u
}

// SetDomainName is an autogenerated method
// nolint: dupl
func (u InfluxdbHostInfoUpdater) SetDomainName(domainName string) InfluxdbHostInfoUpdater {
	u.fields[string(InfluxdbHostInfoDBSchema.DomainName)] = domainName
	return u
}

// SetHostName is an autogenerated method
// nolint: dupl
func (u InfluxdbHostInfoUpdater) SetHostName(hostName string) InfluxdbHostInfoUpdater {
	u.fields[string(InfluxdbHostInfoDBSchema.HostName)] = hostName
	return u
}

// SetPassword is an autogenerated method
// nolint: dupl
func (u InfluxdbHostInfoUpdater) SetPassword(password string) InfluxdbHostInfoUpdater {
	u.fields[string(InfluxdbHostInfoDBSchema.Password)] = password
	return u
}

// SetPort is an autogenerated method
// nolint: dupl
func (u InfluxdbHostInfoUpdater) SetPort(port uint) InfluxdbHostInfoUpdater {
	u.fields[string(InfluxdbHostInfoDBSchema.Port)] = port
	return u
}

// SetProtocol is an autogenerated method
// nolint: dupl
func (u InfluxdbHostInfoUpdater) SetProtocol(protocol string) InfluxdbHostInfoUpdater {
	u.fields[string(InfluxdbHostInfoDBSchema.Protocol)] = protocol
	return u
}

// SetReadRateLimit is an autogenerated method
// nolint: dupl
func (u InfluxdbHostInfoUpdater) SetReadRateLimit(readRateLimit float64) InfluxdbHostInfoUpdater {
	u.fields[string(InfluxdbHostInfoDBSchema.ReadRateLimit)] = readRateLimit
	return u
}

// SetStatus is an autogenerated method
// nolint: dupl
func (u InfluxdbHostInfoUpdater) SetStatus(status bool) InfluxdbHostInfoUpdater {
	u.fields[string(InfluxdbHostInfoDBSchema.Status)] = status
	return u
}

// SetUsername is an autogenerated method
// nolint: dupl
func (u InfluxdbHostInfoUpdater) SetUsername(username string) InfluxdbHostInfoUpdater {
	u.fields[string(InfluxdbHostInfoDBSchema.Username)] = username
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u InfluxdbHostInfoUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u InfluxdbHostInfoUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set InfluxdbHostInfoQuerySet

// ===== BEGIN of InfluxdbHostInfo modifiers

// InfluxdbHostInfoDBSchemaField describes database schema field. It requires for method 'Update'
type InfluxdbHostInfoDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f InfluxdbHostInfoDBSchemaField) String() string {
	return string(f)
}

// InfluxdbHostInfoDBSchema stores db field names of InfluxdbHostInfo
var InfluxdbHostInfoDBSchema = struct {
	HostName        InfluxdbHostInfoDBSchemaField
	DomainName      InfluxdbHostInfoDBSchemaField
	Port            InfluxdbHostInfoDBSchemaField
	Username        InfluxdbHostInfoDBSchemaField
	Password        InfluxdbHostInfoDBSchemaField
	Description     InfluxdbHostInfoDBSchemaField
	Status          InfluxdbHostInfoDBSchemaField
	BackupRateLimit InfluxdbHostInfoDBSchemaField
	ReadRateLimit   InfluxdbHostInfoDBSchemaField
	Protocol        InfluxdbHostInfoDBSchemaField
}{

	HostName:        InfluxdbHostInfoDBSchemaField("host_name"),
	DomainName:      InfluxdbHostInfoDBSchemaField("domain_name"),
	Port:            InfluxdbHostInfoDBSchemaField("port"),
	Username:        InfluxdbHostInfoDBSchemaField("username"),
	Password:        InfluxdbHostInfoDBSchemaField("password"),
	Description:     InfluxdbHostInfoDBSchemaField("description"),
	Status:          InfluxdbHostInfoDBSchemaField("status"),
	BackupRateLimit: InfluxdbHostInfoDBSchemaField("backup_rate_limit"),
	ReadRateLimit:   InfluxdbHostInfoDBSchemaField("read_rate_limit"),
	Protocol:        InfluxdbHostInfoDBSchemaField("protocol"),
}

// Update updates InfluxdbHostInfo fields by primary key
// nolint: dupl
func (o *InfluxdbHostInfo) Update(db *gorm.DB, fields ...InfluxdbHostInfoDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"host_name":         o.HostName,
		"domain_name":       o.DomainName,
		"port":              o.Port,
		"username":          o.Username,
		"password":          o.Password,
		"description":       o.Description,
		"status":            o.Status,
		"backup_rate_limit": o.BackupRateLimit,
		"read_rate_limit":   o.ReadRateLimit,
		"protocol":          o.Protocol,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update InfluxdbHostInfo %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// InfluxdbHostInfoUpdater is an InfluxdbHostInfo updates manager
type InfluxdbHostInfoUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewInfluxdbHostInfoUpdater creates new InfluxdbHostInfo updater
// nolint: dupl
func NewInfluxdbHostInfoUpdater(db *gorm.DB) InfluxdbHostInfoUpdater {
	return InfluxdbHostInfoUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&InfluxdbHostInfo{}),
	}
}

// ===== END of InfluxdbHostInfo modifiers

// ===== END of all query sets
