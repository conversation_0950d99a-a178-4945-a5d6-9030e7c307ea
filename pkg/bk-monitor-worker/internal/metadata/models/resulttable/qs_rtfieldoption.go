// Code generated by go-queryset. DO NOT EDIT.
package resulttable

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set ResultTableFieldOptionQuerySet

// ResultTableFieldOptionQuerySet is an queryset type for ResultTableFieldOption
type ResultTableFieldOptionQuerySet struct {
	db *gorm.DB
}

// NewResultTableFieldOptionQuerySet constructs new ResultTableFieldOptionQuerySet
func NewResultTableFieldOptionQuerySet(db *gorm.DB) ResultTableFieldOptionQuerySet {
	return ResultTableFieldOptionQuerySet{
		db: db.Model(&ResultTableFieldOption{}),
	}
}

func (qs ResultTableFieldOptionQuerySet) w(db *gorm.DB) ResultTableFieldOptionQuerySet {
	return NewResultTableFieldOptionQuerySet(db)
}

func (qs ResultTableFieldOptionQuerySet) Select(fields ...ResultTableFieldOptionDBSchemaField) ResultTableFieldOptionQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *ResultTableFieldOption) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *ResultTableFieldOption) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) All(ret *[]ResultTableFieldOption) error {
	return qs.db.Find(ret).Error
}

// Count is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreateTimeEq(createTime time.Time) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreateTimeGt(createTime time.Time) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreateTimeGte(createTime time.Time) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreateTimeLt(createTime time.Time) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreateTimeLte(createTime time.Time) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreateTimeNe(createTime time.Time) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreatorEq(creator string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreatorGt(creator string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreatorGte(creator string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreatorIn(creator ...string) ResultTableFieldOptionQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreatorLike(creator string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreatorLt(creator string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreatorLte(creator string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreatorNe(creator string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreatorNotIn(creator ...string) ResultTableFieldOptionQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) CreatorNotlike(creator string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) Delete() error {
	return qs.db.Delete(ResultTableFieldOption{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(ResultTableFieldOption{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(ResultTableFieldOption{})
	return db.RowsAffected, db.Error
}

// FieldNameEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) FieldNameEq(fieldName string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("field_name = ?", fieldName))
}

// FieldNameGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) FieldNameGt(fieldName string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("field_name > ?", fieldName))
}

// FieldNameGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) FieldNameGte(fieldName string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("field_name >= ?", fieldName))
}

// FieldNameIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) FieldNameIn(fieldName ...string) ResultTableFieldOptionQuerySet {
	if len(fieldName) == 0 {
		qs.db.AddError(errors.New("must at least pass one fieldName in FieldNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("field_name IN (?)", fieldName))
}

// FieldNameLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) FieldNameLike(fieldName string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("field_name LIKE ?", fieldName))
}

// FieldNameLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) FieldNameLt(fieldName string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("field_name < ?", fieldName))
}

// FieldNameLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) FieldNameLte(fieldName string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("field_name <= ?", fieldName))
}

// FieldNameNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) FieldNameNe(fieldName string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("field_name != ?", fieldName))
}

// FieldNameNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) FieldNameNotIn(fieldName ...string) ResultTableFieldOptionQuerySet {
	if len(fieldName) == 0 {
		qs.db.AddError(errors.New("must at least pass one fieldName in FieldNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("field_name NOT IN (?)", fieldName))
}

// FieldNameNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) FieldNameNotlike(fieldName string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("field_name NOT LIKE ?", fieldName))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) GetUpdater() ResultTableFieldOptionUpdater {
	return NewResultTableFieldOptionUpdater(qs.db)
}

// Limit is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) Limit(limit int) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// NameEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) NameEq(name string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("name = ?", name))
}

// NameGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) NameGt(name string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("name > ?", name))
}

// NameGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) NameGte(name string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("name >= ?", name))
}

// NameIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) NameIn(name ...string) ResultTableFieldOptionQuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name IN (?)", name))
}

// NameLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) NameLike(name string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("name LIKE ?", name))
}

// NameLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) NameLt(name string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("name < ?", name))
}

// NameLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) NameLte(name string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("name <= ?", name))
}

// NameNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) NameNe(name string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("name != ?", name))
}

// NameNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) NameNotIn(name ...string) ResultTableFieldOptionQuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name NOT IN (?)", name))
}

// NameNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) NameNotlike(name string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("name NOT LIKE ?", name))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) Offset(offset int) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs ResultTableFieldOptionQuerySet) One(ret *ResultTableFieldOption) error {
	return qs.db.First(ret).Error
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderAscByCreateTime() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderAscByCreator() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByFieldName is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderAscByFieldName() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("field_name ASC"))
}

// OrderAscByName is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderAscByName() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("name ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderAscByTableID() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderAscByValue is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderAscByValue() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("value ASC"))
}

// OrderAscByValueType is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderAscByValueType() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("value_type ASC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderDescByCreateTime() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderDescByCreator() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByFieldName is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderDescByFieldName() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("field_name DESC"))
}

// OrderDescByName is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderDescByName() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("name DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderDescByTableID() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// OrderDescByValue is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderDescByValue() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("value DESC"))
}

// OrderDescByValueType is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) OrderDescByValueType() ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Order("value_type DESC"))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) TableIDEq(tableID string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) TableIDGt(tableID string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) TableIDGte(tableID string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) TableIDIn(tableID ...string) ResultTableFieldOptionQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) TableIDLike(tableID string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) TableIDLt(tableID string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) TableIDLte(tableID string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) TableIDNe(tableID string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) TableIDNotIn(tableID ...string) ResultTableFieldOptionQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) TableIDNotlike(tableID string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// ValueEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueEq(value string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value = ?", value))
}

// ValueGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueGt(value string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value > ?", value))
}

// ValueGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueGte(value string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value >= ?", value))
}

// ValueIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueIn(value ...string) ResultTableFieldOptionQuerySet {
	if len(value) == 0 {
		qs.db.AddError(errors.New("must at least pass one value in ValueIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("value IN (?)", value))
}

// ValueLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueLike(value string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value LIKE ?", value))
}

// ValueLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueLt(value string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value < ?", value))
}

// ValueLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueLte(value string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value <= ?", value))
}

// ValueNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueNe(value string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value != ?", value))
}

// ValueNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueNotIn(value ...string) ResultTableFieldOptionQuerySet {
	if len(value) == 0 {
		qs.db.AddError(errors.New("must at least pass one value in ValueNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("value NOT IN (?)", value))
}

// ValueNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueNotlike(value string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value NOT LIKE ?", value))
}

// ValueTypeEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueTypeEq(valueType string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value_type = ?", valueType))
}

// ValueTypeGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueTypeGt(valueType string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value_type > ?", valueType))
}

// ValueTypeGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueTypeGte(valueType string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value_type >= ?", valueType))
}

// ValueTypeIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueTypeIn(valueType ...string) ResultTableFieldOptionQuerySet {
	if len(valueType) == 0 {
		qs.db.AddError(errors.New("must at least pass one valueType in ValueTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("value_type IN (?)", valueType))
}

// ValueTypeLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueTypeLike(valueType string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value_type LIKE ?", valueType))
}

// ValueTypeLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueTypeLt(valueType string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value_type < ?", valueType))
}

// ValueTypeLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueTypeLte(valueType string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value_type <= ?", valueType))
}

// ValueTypeNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueTypeNe(valueType string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value_type != ?", valueType))
}

// ValueTypeNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueTypeNotIn(valueType ...string) ResultTableFieldOptionQuerySet {
	if len(valueType) == 0 {
		qs.db.AddError(errors.New("must at least pass one valueType in ValueTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("value_type NOT IN (?)", valueType))
}

// ValueTypeNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldOptionQuerySet) ValueTypeNotlike(valueType string) ResultTableFieldOptionQuerySet {
	return qs.w(qs.db.Where("value_type NOT LIKE ?", valueType))
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u ResultTableFieldOptionUpdater) SetCreateTime(createTime time.Time) ResultTableFieldOptionUpdater {
	u.fields[string(ResultTableFieldOptionDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u ResultTableFieldOptionUpdater) SetCreator(creator string) ResultTableFieldOptionUpdater {
	u.fields[string(ResultTableFieldOptionDBSchema.Creator)] = creator
	return u
}

// SetFieldName is an autogenerated method
// nolint: dupl
func (u ResultTableFieldOptionUpdater) SetFieldName(fieldName string) ResultTableFieldOptionUpdater {
	u.fields[string(ResultTableFieldOptionDBSchema.FieldName)] = fieldName
	return u
}

// SetName is an autogenerated method
// nolint: dupl
func (u ResultTableFieldOptionUpdater) SetName(name string) ResultTableFieldOptionUpdater {
	u.fields[string(ResultTableFieldOptionDBSchema.Name)] = name
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u ResultTableFieldOptionUpdater) SetTableID(tableID string) ResultTableFieldOptionUpdater {
	u.fields[string(ResultTableFieldOptionDBSchema.TableID)] = tableID
	return u
}

// SetValue is an autogenerated method
// nolint: dupl
func (u ResultTableFieldOptionUpdater) SetValue(value string) ResultTableFieldOptionUpdater {
	u.fields[string(ResultTableFieldOptionDBSchema.Value)] = value
	return u
}

// SetValueType is an autogenerated method
// nolint: dupl
func (u ResultTableFieldOptionUpdater) SetValueType(valueType string) ResultTableFieldOptionUpdater {
	u.fields[string(ResultTableFieldOptionDBSchema.ValueType)] = valueType
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u ResultTableFieldOptionUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u ResultTableFieldOptionUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set ResultTableFieldOptionQuerySet

// ===== BEGIN of ResultTableFieldOption modifiers

// ResultTableFieldOptionDBSchemaField describes database schema field. It requires for method 'Update'
type ResultTableFieldOptionDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f ResultTableFieldOptionDBSchemaField) String() string {
	return string(f)
}

// ResultTableFieldOptionDBSchema stores db field names of ResultTableFieldOption
var ResultTableFieldOptionDBSchema = struct {
	ValueType  ResultTableFieldOptionDBSchemaField
	Value      ResultTableFieldOptionDBSchemaField
	Creator    ResultTableFieldOptionDBSchemaField
	CreateTime ResultTableFieldOptionDBSchemaField
	TableID    ResultTableFieldOptionDBSchemaField
	FieldName  ResultTableFieldOptionDBSchemaField
	Name       ResultTableFieldOptionDBSchemaField
}{

	ValueType:  ResultTableFieldOptionDBSchemaField("value_type"),
	Value:      ResultTableFieldOptionDBSchemaField("value"),
	Creator:    ResultTableFieldOptionDBSchemaField("creator"),
	CreateTime: ResultTableFieldOptionDBSchemaField("create_time"),
	TableID:    ResultTableFieldOptionDBSchemaField("table_id"),
	FieldName:  ResultTableFieldOptionDBSchemaField("field_name"),
	Name:       ResultTableFieldOptionDBSchemaField("name"),
}

// Update updates ResultTableFieldOption fields by primary key
// nolint: dupl
func (o *ResultTableFieldOption) Update(db *gorm.DB, fields ...ResultTableFieldOptionDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"value_type":  o.ValueType,
		"value":       o.Value,
		"creator":     o.Creator,
		"create_time": o.CreateTime,
		"table_id":    o.TableID,
		"field_name":  o.FieldName,
		"name":        o.Name,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update ResultTableFieldOption %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// ResultTableFieldOptionUpdater is an ResultTableFieldOption updates manager
type ResultTableFieldOptionUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewResultTableFieldOptionUpdater creates new ResultTableFieldOption updater
// nolint: dupl
func NewResultTableFieldOptionUpdater(db *gorm.DB) ResultTableFieldOptionUpdater {
	return ResultTableFieldOptionUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&ResultTableFieldOption{}),
	}
}

// ===== END of ResultTableFieldOption modifiers

// ===== END of all query sets
