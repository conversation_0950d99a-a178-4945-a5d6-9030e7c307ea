// Code generated by go-queryset. DO NOT EDIT.
package resulttable

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set ResultTableFieldQuerySet

// ResultTableFieldQuerySet is an queryset type for ResultTableField
type ResultTableFieldQuerySet struct {
	db *gorm.DB
}

// NewResultTableFieldQuerySet constructs new ResultTableFieldQuerySet
func NewResultTableFieldQuerySet(db *gorm.DB) ResultTableFieldQuerySet {
	return ResultTableFieldQuerySet{
		db: db.Model(&ResultTableField{}),
	}
}

func (qs ResultTableFieldQuerySet) w(db *gorm.DB) ResultTableFieldQuerySet {
	return NewResultTableFieldQuerySet(db)
}

func (qs ResultTableFieldQuerySet) Select(fields ...ResultTableFieldDBSchemaField) ResultTableFieldQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *ResultTableField) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *ResultTableField) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// AliasNameEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) AliasNameEq(aliasName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("alias_name = ?", aliasName))
}

// AliasNameGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) AliasNameGt(aliasName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("alias_name > ?", aliasName))
}

// AliasNameGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) AliasNameGte(aliasName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("alias_name >= ?", aliasName))
}

// AliasNameIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) AliasNameIn(aliasName ...string) ResultTableFieldQuerySet {
	if len(aliasName) == 0 {
		qs.db.AddError(errors.New("must at least pass one aliasName in AliasNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("alias_name IN (?)", aliasName))
}

// AliasNameLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) AliasNameLike(aliasName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("alias_name LIKE ?", aliasName))
}

// AliasNameLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) AliasNameLt(aliasName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("alias_name < ?", aliasName))
}

// AliasNameLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) AliasNameLte(aliasName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("alias_name <= ?", aliasName))
}

// AliasNameNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) AliasNameNe(aliasName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("alias_name != ?", aliasName))
}

// AliasNameNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) AliasNameNotIn(aliasName ...string) ResultTableFieldQuerySet {
	if len(aliasName) == 0 {
		qs.db.AddError(errors.New("must at least pass one aliasName in AliasNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("alias_name NOT IN (?)", aliasName))
}

// AliasNameNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) AliasNameNotlike(aliasName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("alias_name NOT LIKE ?", aliasName))
}

// All is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) All(ret *[]ResultTableField) error {
	return qs.db.Find(ret).Error
}

// Count is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreateTimeEq(createTime time.Time) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreateTimeGt(createTime time.Time) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreateTimeGte(createTime time.Time) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreateTimeLt(createTime time.Time) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreateTimeLte(createTime time.Time) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreateTimeNe(createTime time.Time) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreatorEq(creator string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreatorGt(creator string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreatorGte(creator string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreatorIn(creator ...string) ResultTableFieldQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreatorLike(creator string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreatorLt(creator string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreatorLte(creator string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreatorNe(creator string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreatorNotIn(creator ...string) ResultTableFieldQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) CreatorNotlike(creator string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// DefaultValueEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DefaultValueEq(defaultValue string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("default_value = ?", defaultValue))
}

// DefaultValueGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DefaultValueGt(defaultValue string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("default_value > ?", defaultValue))
}

// DefaultValueGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DefaultValueGte(defaultValue string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("default_value >= ?", defaultValue))
}

// DefaultValueIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DefaultValueIn(defaultValue ...string) ResultTableFieldQuerySet {
	if len(defaultValue) == 0 {
		qs.db.AddError(errors.New("must at least pass one defaultValue in DefaultValueIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("default_value IN (?)", defaultValue))
}

// DefaultValueLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DefaultValueLike(defaultValue string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("default_value LIKE ?", defaultValue))
}

// DefaultValueLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DefaultValueLt(defaultValue string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("default_value < ?", defaultValue))
}

// DefaultValueLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DefaultValueLte(defaultValue string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("default_value <= ?", defaultValue))
}

// DefaultValueNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DefaultValueNe(defaultValue string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("default_value != ?", defaultValue))
}

// DefaultValueNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DefaultValueNotIn(defaultValue ...string) ResultTableFieldQuerySet {
	if len(defaultValue) == 0 {
		qs.db.AddError(errors.New("must at least pass one defaultValue in DefaultValueNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("default_value NOT IN (?)", defaultValue))
}

// DefaultValueNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DefaultValueNotlike(defaultValue string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("default_value NOT LIKE ?", defaultValue))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) Delete() error {
	return qs.db.Delete(ResultTableField{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(ResultTableField{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(ResultTableField{})
	return db.RowsAffected, db.Error
}

// DescriptionEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DescriptionEq(description string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("description = ?", description))
}

// DescriptionGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DescriptionGt(description string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("description > ?", description))
}

// DescriptionGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DescriptionGte(description string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("description >= ?", description))
}

// DescriptionIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DescriptionIn(description ...string) ResultTableFieldQuerySet {
	if len(description) == 0 {
		qs.db.AddError(errors.New("must at least pass one description in DescriptionIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("description IN (?)", description))
}

// DescriptionLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DescriptionLike(description string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("description LIKE ?", description))
}

// DescriptionLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DescriptionLt(description string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("description < ?", description))
}

// DescriptionLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DescriptionLte(description string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("description <= ?", description))
}

// DescriptionNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DescriptionNe(description string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("description != ?", description))
}

// DescriptionNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DescriptionNotIn(description ...string) ResultTableFieldQuerySet {
	if len(description) == 0 {
		qs.db.AddError(errors.New("must at least pass one description in DescriptionNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("description NOT IN (?)", description))
}

// DescriptionNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) DescriptionNotlike(description string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("description NOT LIKE ?", description))
}

// FieldNameEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldNameEq(fieldName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_name = ?", fieldName))
}

// FieldNameGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldNameGt(fieldName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_name > ?", fieldName))
}

// FieldNameGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldNameGte(fieldName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_name >= ?", fieldName))
}

// FieldNameIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldNameIn(fieldName ...string) ResultTableFieldQuerySet {
	if len(fieldName) == 0 {
		qs.db.AddError(errors.New("must at least pass one fieldName in FieldNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("field_name IN (?)", fieldName))
}

// FieldNameLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldNameLike(fieldName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_name LIKE ?", fieldName))
}

// FieldNameLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldNameLt(fieldName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_name < ?", fieldName))
}

// FieldNameLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldNameLte(fieldName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_name <= ?", fieldName))
}

// FieldNameNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldNameNe(fieldName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_name != ?", fieldName))
}

// FieldNameNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldNameNotIn(fieldName ...string) ResultTableFieldQuerySet {
	if len(fieldName) == 0 {
		qs.db.AddError(errors.New("must at least pass one fieldName in FieldNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("field_name NOT IN (?)", fieldName))
}

// FieldNameNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldNameNotlike(fieldName string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_name NOT LIKE ?", fieldName))
}

// FieldTypeEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldTypeEq(fieldType string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_type = ?", fieldType))
}

// FieldTypeGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldTypeGt(fieldType string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_type > ?", fieldType))
}

// FieldTypeGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldTypeGte(fieldType string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_type >= ?", fieldType))
}

// FieldTypeIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldTypeIn(fieldType ...string) ResultTableFieldQuerySet {
	if len(fieldType) == 0 {
		qs.db.AddError(errors.New("must at least pass one fieldType in FieldTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("field_type IN (?)", fieldType))
}

// FieldTypeLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldTypeLike(fieldType string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_type LIKE ?", fieldType))
}

// FieldTypeLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldTypeLt(fieldType string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_type < ?", fieldType))
}

// FieldTypeLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldTypeLte(fieldType string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_type <= ?", fieldType))
}

// FieldTypeNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldTypeNe(fieldType string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_type != ?", fieldType))
}

// FieldTypeNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldTypeNotIn(fieldType ...string) ResultTableFieldQuerySet {
	if len(fieldType) == 0 {
		qs.db.AddError(errors.New("must at least pass one fieldType in FieldTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("field_type NOT IN (?)", fieldType))
}

// FieldTypeNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) FieldTypeNotlike(fieldType string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("field_type NOT LIKE ?", fieldType))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) GetUpdater() ResultTableFieldUpdater {
	return NewResultTableFieldUpdater(qs.db)
}

// IsConfigByUserEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) IsConfigByUserEq(isConfigByUser bool) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("is_config_by_user = ?", isConfigByUser))
}

// IsConfigByUserIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) IsConfigByUserIn(isConfigByUser ...bool) ResultTableFieldQuerySet {
	if len(isConfigByUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one isConfigByUser in IsConfigByUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_config_by_user IN (?)", isConfigByUser))
}

// IsConfigByUserNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) IsConfigByUserNe(isConfigByUser bool) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("is_config_by_user != ?", isConfigByUser))
}

// IsConfigByUserNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) IsConfigByUserNotIn(isConfigByUser ...bool) ResultTableFieldQuerySet {
	if len(isConfigByUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one isConfigByUser in IsConfigByUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_config_by_user NOT IN (?)", isConfigByUser))
}

// IsDisabledEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) IsDisabledEq(isDisabled bool) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("is_disabled = ?", isDisabled))
}

// IsDisabledIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) IsDisabledIn(isDisabled ...bool) ResultTableFieldQuerySet {
	if len(isDisabled) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDisabled in IsDisabledIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_disabled IN (?)", isDisabled))
}

// IsDisabledNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) IsDisabledNe(isDisabled bool) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("is_disabled != ?", isDisabled))
}

// IsDisabledNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) IsDisabledNotIn(isDisabled ...bool) ResultTableFieldQuerySet {
	if len(isDisabled) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDisabled in IsDisabledNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_disabled NOT IN (?)", isDisabled))
}

// LastModifyTimeEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyTimeEq(lastModifyTime time.Time) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_time = ?", lastModifyTime))
}

// LastModifyTimeGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyTimeGt(lastModifyTime time.Time) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_time > ?", lastModifyTime))
}

// LastModifyTimeGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyTimeGte(lastModifyTime time.Time) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_time >= ?", lastModifyTime))
}

// LastModifyTimeLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyTimeLt(lastModifyTime time.Time) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_time < ?", lastModifyTime))
}

// LastModifyTimeLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyTimeLte(lastModifyTime time.Time) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_time <= ?", lastModifyTime))
}

// LastModifyTimeNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyTimeNe(lastModifyTime time.Time) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_time != ?", lastModifyTime))
}

// LastModifyUserEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyUserEq(lastModifyUser string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_user = ?", lastModifyUser))
}

// LastModifyUserGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyUserGt(lastModifyUser string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_user > ?", lastModifyUser))
}

// LastModifyUserGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyUserGte(lastModifyUser string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_user >= ?", lastModifyUser))
}

// LastModifyUserIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyUserIn(lastModifyUser ...string) ResultTableFieldQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user IN (?)", lastModifyUser))
}

// LastModifyUserLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyUserLike(lastModifyUser string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_user LIKE ?", lastModifyUser))
}

// LastModifyUserLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyUserLt(lastModifyUser string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_user < ?", lastModifyUser))
}

// LastModifyUserLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyUserLte(lastModifyUser string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_user <= ?", lastModifyUser))
}

// LastModifyUserNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyUserNe(lastModifyUser string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_user != ?", lastModifyUser))
}

// LastModifyUserNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyUserNotIn(lastModifyUser ...string) ResultTableFieldQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user NOT IN (?)", lastModifyUser))
}

// LastModifyUserNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) LastModifyUserNotlike(lastModifyUser string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("last_modify_user NOT LIKE ?", lastModifyUser))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) Limit(limit int) ResultTableFieldQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) Offset(offset int) ResultTableFieldQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs ResultTableFieldQuerySet) One(ret *ResultTableField) error {
	return qs.db.First(ret).Error
}

// OrderAscByAliasName is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByAliasName() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("alias_name ASC"))
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByCreateTime() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByCreator() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByDefaultValue is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByDefaultValue() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("default_value ASC"))
}

// OrderAscByDescription is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByDescription() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("description ASC"))
}

// OrderAscByFieldName is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByFieldName() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("field_name ASC"))
}

// OrderAscByFieldType is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByFieldType() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("field_type ASC"))
}

// OrderAscByIsConfigByUser is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByIsConfigByUser() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("is_config_by_user ASC"))
}

// OrderAscByIsDisabled is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByIsDisabled() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("is_disabled ASC"))
}

// OrderAscByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByLastModifyTime() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("last_modify_time ASC"))
}

// OrderAscByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByLastModifyUser() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("last_modify_user ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByTableID() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderAscByTag is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByTag() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("tag ASC"))
}

// OrderAscByUnit is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderAscByUnit() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("unit ASC"))
}

// OrderDescByAliasName is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByAliasName() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("alias_name DESC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByCreateTime() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByCreator() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByDefaultValue is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByDefaultValue() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("default_value DESC"))
}

// OrderDescByDescription is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByDescription() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("description DESC"))
}

// OrderDescByFieldName is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByFieldName() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("field_name DESC"))
}

// OrderDescByFieldType is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByFieldType() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("field_type DESC"))
}

// OrderDescByIsConfigByUser is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByIsConfigByUser() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("is_config_by_user DESC"))
}

// OrderDescByIsDisabled is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByIsDisabled() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("is_disabled DESC"))
}

// OrderDescByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByLastModifyTime() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("last_modify_time DESC"))
}

// OrderDescByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByLastModifyUser() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("last_modify_user DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByTableID() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// OrderDescByTag is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByTag() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("tag DESC"))
}

// OrderDescByUnit is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) OrderDescByUnit() ResultTableFieldQuerySet {
	return qs.w(qs.db.Order("unit DESC"))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TableIDEq(tableID string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TableIDGt(tableID string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TableIDGte(tableID string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TableIDIn(tableID ...string) ResultTableFieldQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TableIDLike(tableID string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TableIDLt(tableID string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TableIDLte(tableID string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TableIDNe(tableID string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TableIDNotIn(tableID ...string) ResultTableFieldQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TableIDNotlike(tableID string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// TagEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TagEq(tag string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("tag = ?", tag))
}

// TagGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TagGt(tag string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("tag > ?", tag))
}

// TagGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TagGte(tag string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("tag >= ?", tag))
}

// TagIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TagIn(tag ...string) ResultTableFieldQuerySet {
	if len(tag) == 0 {
		qs.db.AddError(errors.New("must at least pass one tag in TagIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("tag IN (?)", tag))
}

// TagLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TagLike(tag string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("tag LIKE ?", tag))
}

// TagLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TagLt(tag string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("tag < ?", tag))
}

// TagLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TagLte(tag string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("tag <= ?", tag))
}

// TagNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TagNe(tag string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("tag != ?", tag))
}

// TagNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TagNotIn(tag ...string) ResultTableFieldQuerySet {
	if len(tag) == 0 {
		qs.db.AddError(errors.New("must at least pass one tag in TagNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("tag NOT IN (?)", tag))
}

// TagNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) TagNotlike(tag string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("tag NOT LIKE ?", tag))
}

// UnitEq is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) UnitEq(unit string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("unit = ?", unit))
}

// UnitGt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) UnitGt(unit string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("unit > ?", unit))
}

// UnitGte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) UnitGte(unit string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("unit >= ?", unit))
}

// UnitIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) UnitIn(unit ...string) ResultTableFieldQuerySet {
	if len(unit) == 0 {
		qs.db.AddError(errors.New("must at least pass one unit in UnitIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("unit IN (?)", unit))
}

// UnitLike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) UnitLike(unit string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("unit LIKE ?", unit))
}

// UnitLt is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) UnitLt(unit string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("unit < ?", unit))
}

// UnitLte is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) UnitLte(unit string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("unit <= ?", unit))
}

// UnitNe is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) UnitNe(unit string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("unit != ?", unit))
}

// UnitNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) UnitNotIn(unit ...string) ResultTableFieldQuerySet {
	if len(unit) == 0 {
		qs.db.AddError(errors.New("must at least pass one unit in UnitNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("unit NOT IN (?)", unit))
}

// UnitNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableFieldQuerySet) UnitNotlike(unit string) ResultTableFieldQuerySet {
	return qs.w(qs.db.Where("unit NOT LIKE ?", unit))
}

// SetAliasName is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetAliasName(aliasName string) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.AliasName)] = aliasName
	return u
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetCreateTime(createTime time.Time) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetCreator(creator string) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.Creator)] = creator
	return u
}

// SetDefaultValue is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetDefaultValue(defaultValue string) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.DefaultValue)] = defaultValue
	return u
}

// SetDescription is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetDescription(description string) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.Description)] = description
	return u
}

// SetFieldName is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetFieldName(fieldName string) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.FieldName)] = fieldName
	return u
}

// SetFieldType is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetFieldType(fieldType string) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.FieldType)] = fieldType
	return u
}

// SetIsConfigByUser is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetIsConfigByUser(isConfigByUser bool) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.IsConfigByUser)] = isConfigByUser
	return u
}

// SetIsDisabled is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetIsDisabled(isDisabled bool) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.IsDisabled)] = isDisabled
	return u
}

// SetLastModifyTime is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetLastModifyTime(lastModifyTime time.Time) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.LastModifyTime)] = lastModifyTime
	return u
}

// SetLastModifyUser is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetLastModifyUser(lastModifyUser string) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.LastModifyUser)] = lastModifyUser
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetTableID(tableID string) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.TableID)] = tableID
	return u
}

// SetTag is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetTag(tag string) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.Tag)] = tag
	return u
}

// SetUnit is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) SetUnit(unit string) ResultTableFieldUpdater {
	u.fields[string(ResultTableFieldDBSchema.Unit)] = unit
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u ResultTableFieldUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set ResultTableFieldQuerySet

// ===== BEGIN of ResultTableField modifiers

// ResultTableFieldDBSchemaField describes database schema field. It requires for method 'Update'
type ResultTableFieldDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f ResultTableFieldDBSchemaField) String() string {
	return string(f)
}

// ResultTableFieldDBSchema stores db field names of ResultTableField
var ResultTableFieldDBSchema = struct {
	TableID        ResultTableFieldDBSchemaField
	FieldName      ResultTableFieldDBSchemaField
	FieldType      ResultTableFieldDBSchemaField
	Description    ResultTableFieldDBSchemaField
	Unit           ResultTableFieldDBSchemaField
	Tag            ResultTableFieldDBSchemaField
	IsConfigByUser ResultTableFieldDBSchemaField
	DefaultValue   ResultTableFieldDBSchemaField
	Creator        ResultTableFieldDBSchemaField
	CreateTime     ResultTableFieldDBSchemaField
	LastModifyUser ResultTableFieldDBSchemaField
	LastModifyTime ResultTableFieldDBSchemaField
	AliasName      ResultTableFieldDBSchemaField
	IsDisabled     ResultTableFieldDBSchemaField
}{

	TableID:        ResultTableFieldDBSchemaField("table_id"),
	FieldName:      ResultTableFieldDBSchemaField("field_name"),
	FieldType:      ResultTableFieldDBSchemaField("field_type"),
	Description:    ResultTableFieldDBSchemaField("description"),
	Unit:           ResultTableFieldDBSchemaField("unit"),
	Tag:            ResultTableFieldDBSchemaField("tag"),
	IsConfigByUser: ResultTableFieldDBSchemaField("is_config_by_user"),
	DefaultValue:   ResultTableFieldDBSchemaField("default_value"),
	Creator:        ResultTableFieldDBSchemaField("creator"),
	CreateTime:     ResultTableFieldDBSchemaField("create_time"),
	LastModifyUser: ResultTableFieldDBSchemaField("last_modify_user"),
	LastModifyTime: ResultTableFieldDBSchemaField("last_modify_time"),
	AliasName:      ResultTableFieldDBSchemaField("alias_name"),
	IsDisabled:     ResultTableFieldDBSchemaField("is_disabled"),
}

// Update updates ResultTableField fields by primary key
// nolint: dupl
func (o *ResultTableField) Update(db *gorm.DB, fields ...ResultTableFieldDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"table_id":          o.TableID,
		"field_name":        o.FieldName,
		"field_type":        o.FieldType,
		"description":       o.Description,
		"unit":              o.Unit,
		"tag":               o.Tag,
		"is_config_by_user": o.IsConfigByUser,
		"default_value":     o.DefaultValue,
		"creator":           o.Creator,
		"create_time":       o.CreateTime,
		"last_modify_user":  o.LastModifyUser,
		"last_modify_time":  o.LastModifyTime,
		"alias_name":        o.AliasName,
		"is_disabled":       o.IsDisabled,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update ResultTableField %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// ResultTableFieldUpdater is an ResultTableField updates manager
type ResultTableFieldUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewResultTableFieldUpdater creates new ResultTableField updater
// nolint: dupl
func NewResultTableFieldUpdater(db *gorm.DB) ResultTableFieldUpdater {
	return ResultTableFieldUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&ResultTableField{}),
	}
}

// ===== END of ResultTableField modifiers

// ===== END of all query sets
