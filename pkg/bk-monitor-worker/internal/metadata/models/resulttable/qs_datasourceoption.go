// Code generated by go-queryset. DO NOT EDIT.
package resulttable

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set DataSourceOptionQuerySet

// DataSourceOptionQuerySet is an queryset type for DataSourceOption
type DataSourceOptionQuerySet struct {
	db *gorm.DB
}

// NewDataSourceOptionQuerySet constructs new DataSourceOptionQuerySet
func NewDataSourceOptionQuerySet(db *gorm.DB) DataSourceOptionQuerySet {
	return DataSourceOptionQuerySet{
		db: db.Model(&DataSourceOption{}),
	}
}

func (qs DataSourceOptionQuerySet) w(db *gorm.DB) DataSourceOptionQuerySet {
	return NewDataSourceOptionQuerySet(db)
}

func (qs DataSourceOptionQuerySet) Select(fields ...DataSourceOptionDBSchemaField) DataSourceOptionQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *DataSourceOption) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *DataSourceOption) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) All(ret *[]DataSourceOption) error {
	return qs.db.Find(ret).Error
}

// BkDataIdEq is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) BkDataIdEq(bkDataId uint) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("bk_data_id = ?", bkDataId))
}

// BkDataIdGt is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) BkDataIdGt(bkDataId uint) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("bk_data_id > ?", bkDataId))
}

// BkDataIdGte is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) BkDataIdGte(bkDataId uint) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("bk_data_id >= ?", bkDataId))
}

// BkDataIdIn is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) BkDataIdIn(bkDataId ...uint) DataSourceOptionQuerySet {
	if len(bkDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataId in BkDataIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id IN (?)", bkDataId))
}

// BkDataIdLt is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) BkDataIdLt(bkDataId uint) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("bk_data_id < ?", bkDataId))
}

// BkDataIdLte is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) BkDataIdLte(bkDataId uint) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("bk_data_id <= ?", bkDataId))
}

// BkDataIdNe is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) BkDataIdNe(bkDataId uint) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("bk_data_id != ?", bkDataId))
}

// BkDataIdNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) BkDataIdNotIn(bkDataId ...uint) DataSourceOptionQuerySet {
	if len(bkDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataId in BkDataIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id NOT IN (?)", bkDataId))
}

// Count is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreateTimeEq(createTime time.Time) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreateTimeGt(createTime time.Time) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreateTimeGte(createTime time.Time) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreateTimeLt(createTime time.Time) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreateTimeLte(createTime time.Time) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreateTimeNe(createTime time.Time) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreatorEq(creator string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreatorGt(creator string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreatorGte(creator string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreatorIn(creator ...string) DataSourceOptionQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreatorLike(creator string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreatorLt(creator string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreatorLte(creator string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreatorNe(creator string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreatorNotIn(creator ...string) DataSourceOptionQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) CreatorNotlike(creator string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) Delete() error {
	return qs.db.Delete(DataSourceOption{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(DataSourceOption{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(DataSourceOption{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) GetUpdater() DataSourceOptionUpdater {
	return NewDataSourceOptionUpdater(qs.db)
}

// Limit is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) Limit(limit int) DataSourceOptionQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// NameEq is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) NameEq(name string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("name = ?", name))
}

// NameGt is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) NameGt(name string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("name > ?", name))
}

// NameGte is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) NameGte(name string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("name >= ?", name))
}

// NameIn is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) NameIn(name ...string) DataSourceOptionQuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name IN (?)", name))
}

// NameLike is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) NameLike(name string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("name LIKE ?", name))
}

// NameLt is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) NameLt(name string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("name < ?", name))
}

// NameLte is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) NameLte(name string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("name <= ?", name))
}

// NameNe is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) NameNe(name string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("name != ?", name))
}

// NameNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) NameNotIn(name ...string) DataSourceOptionQuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name NOT IN (?)", name))
}

// NameNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) NameNotlike(name string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("name NOT LIKE ?", name))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) Offset(offset int) DataSourceOptionQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs DataSourceOptionQuerySet) One(ret *DataSourceOption) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkDataId is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) OrderAscByBkDataId() DataSourceOptionQuerySet {
	return qs.w(qs.db.Order("bk_data_id ASC"))
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) OrderAscByCreateTime() DataSourceOptionQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) OrderAscByCreator() DataSourceOptionQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByName is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) OrderAscByName() DataSourceOptionQuerySet {
	return qs.w(qs.db.Order("name ASC"))
}

// OrderAscByValue is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) OrderAscByValue() DataSourceOptionQuerySet {
	return qs.w(qs.db.Order("value ASC"))
}

// OrderAscByValueType is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) OrderAscByValueType() DataSourceOptionQuerySet {
	return qs.w(qs.db.Order("value_type ASC"))
}

// OrderDescByBkDataId is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) OrderDescByBkDataId() DataSourceOptionQuerySet {
	return qs.w(qs.db.Order("bk_data_id DESC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) OrderDescByCreateTime() DataSourceOptionQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) OrderDescByCreator() DataSourceOptionQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByName is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) OrderDescByName() DataSourceOptionQuerySet {
	return qs.w(qs.db.Order("name DESC"))
}

// OrderDescByValue is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) OrderDescByValue() DataSourceOptionQuerySet {
	return qs.w(qs.db.Order("value DESC"))
}

// OrderDescByValueType is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) OrderDescByValueType() DataSourceOptionQuerySet {
	return qs.w(qs.db.Order("value_type DESC"))
}

// ValueEq is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueEq(value string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value = ?", value))
}

// ValueGt is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueGt(value string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value > ?", value))
}

// ValueGte is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueGte(value string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value >= ?", value))
}

// ValueIn is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueIn(value ...string) DataSourceOptionQuerySet {
	if len(value) == 0 {
		qs.db.AddError(errors.New("must at least pass one value in ValueIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("value IN (?)", value))
}

// ValueLike is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueLike(value string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value LIKE ?", value))
}

// ValueLt is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueLt(value string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value < ?", value))
}

// ValueLte is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueLte(value string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value <= ?", value))
}

// ValueNe is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueNe(value string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value != ?", value))
}

// ValueNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueNotIn(value ...string) DataSourceOptionQuerySet {
	if len(value) == 0 {
		qs.db.AddError(errors.New("must at least pass one value in ValueNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("value NOT IN (?)", value))
}

// ValueNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueNotlike(value string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value NOT LIKE ?", value))
}

// ValueTypeEq is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueTypeEq(valueType string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value_type = ?", valueType))
}

// ValueTypeGt is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueTypeGt(valueType string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value_type > ?", valueType))
}

// ValueTypeGte is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueTypeGte(valueType string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value_type >= ?", valueType))
}

// ValueTypeIn is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueTypeIn(valueType ...string) DataSourceOptionQuerySet {
	if len(valueType) == 0 {
		qs.db.AddError(errors.New("must at least pass one valueType in ValueTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("value_type IN (?)", valueType))
}

// ValueTypeLike is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueTypeLike(valueType string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value_type LIKE ?", valueType))
}

// ValueTypeLt is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueTypeLt(valueType string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value_type < ?", valueType))
}

// ValueTypeLte is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueTypeLte(valueType string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value_type <= ?", valueType))
}

// ValueTypeNe is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueTypeNe(valueType string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value_type != ?", valueType))
}

// ValueTypeNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueTypeNotIn(valueType ...string) DataSourceOptionQuerySet {
	if len(valueType) == 0 {
		qs.db.AddError(errors.New("must at least pass one valueType in ValueTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("value_type NOT IN (?)", valueType))
}

// ValueTypeNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceOptionQuerySet) ValueTypeNotlike(valueType string) DataSourceOptionQuerySet {
	return qs.w(qs.db.Where("value_type NOT LIKE ?", valueType))
}

// SetBkDataId is an autogenerated method
// nolint: dupl
func (u DataSourceOptionUpdater) SetBkDataId(bkDataId uint) DataSourceOptionUpdater {
	u.fields[string(DataSourceOptionDBSchema.BkDataId)] = bkDataId
	return u
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u DataSourceOptionUpdater) SetCreateTime(createTime time.Time) DataSourceOptionUpdater {
	u.fields[string(DataSourceOptionDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u DataSourceOptionUpdater) SetCreator(creator string) DataSourceOptionUpdater {
	u.fields[string(DataSourceOptionDBSchema.Creator)] = creator
	return u
}

// SetName is an autogenerated method
// nolint: dupl
func (u DataSourceOptionUpdater) SetName(name string) DataSourceOptionUpdater {
	u.fields[string(DataSourceOptionDBSchema.Name)] = name
	return u
}

// SetValue is an autogenerated method
// nolint: dupl
func (u DataSourceOptionUpdater) SetValue(value string) DataSourceOptionUpdater {
	u.fields[string(DataSourceOptionDBSchema.Value)] = value
	return u
}

// SetValueType is an autogenerated method
// nolint: dupl
func (u DataSourceOptionUpdater) SetValueType(valueType string) DataSourceOptionUpdater {
	u.fields[string(DataSourceOptionDBSchema.ValueType)] = valueType
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u DataSourceOptionUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u DataSourceOptionUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set DataSourceOptionQuerySet

// ===== BEGIN of DataSourceOption modifiers

// DataSourceOptionDBSchemaField describes database schema field. It requires for method 'Update'
type DataSourceOptionDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f DataSourceOptionDBSchemaField) String() string {
	return string(f)
}

// DataSourceOptionDBSchema stores db field names of DataSourceOption
var DataSourceOptionDBSchema = struct {
	ValueType  DataSourceOptionDBSchemaField
	Value      DataSourceOptionDBSchemaField
	Creator    DataSourceOptionDBSchemaField
	CreateTime DataSourceOptionDBSchemaField
	BkDataId   DataSourceOptionDBSchemaField
	Name       DataSourceOptionDBSchemaField
}{

	ValueType:  DataSourceOptionDBSchemaField("value_type"),
	Value:      DataSourceOptionDBSchemaField("value"),
	Creator:    DataSourceOptionDBSchemaField("creator"),
	CreateTime: DataSourceOptionDBSchemaField("create_time"),
	BkDataId:   DataSourceOptionDBSchemaField("bk_data_id"),
	Name:       DataSourceOptionDBSchemaField("name"),
}

// Update updates DataSourceOption fields by primary key
// nolint: dupl
func (o *DataSourceOption) Update(db *gorm.DB, fields ...DataSourceOptionDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"value_type":  o.ValueType,
		"value":       o.Value,
		"creator":     o.Creator,
		"create_time": o.CreateTime,
		"bk_data_id":  o.BkDataId,
		"name":        o.Name,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update DataSourceOption %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// DataSourceOptionUpdater is an DataSourceOption updates manager
type DataSourceOptionUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewDataSourceOptionUpdater creates new DataSourceOption updater
// nolint: dupl
func NewDataSourceOptionUpdater(db *gorm.DB) DataSourceOptionUpdater {
	return DataSourceOptionUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&DataSourceOption{}),
	}
}

// ===== END of DataSourceOption modifiers

// ===== END of all query sets
