// Code generated by go-queryset. DO NOT EDIT.
package resulttable

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set ResultTableQuerySet

// ResultTableQuerySet is an queryset type for ResultTable
type ResultTableQuerySet struct {
	db *gorm.DB
}

// NewResultTableQuerySet constructs new ResultTableQuerySet
func NewResultTableQuerySet(db *gorm.DB) ResultTableQuerySet {
	return ResultTableQuerySet{
		db: db.Model(&ResultTable{}),
	}
}

func (qs ResultTableQuerySet) w(db *gorm.DB) ResultTableQuerySet {
	return NewResultTableQuerySet(db)
}

func (qs ResultTableQuerySet) Select(fields ...ResultTableDBSchemaField) ResultTableQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *ResultTable) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *ResultTable) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) All(ret *[]ResultTable) error {
	return qs.db.Find(ret).Error
}

// BkBizIdEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) BkBizIdEq(bkBizId int) ResultTableQuerySet {
	return qs.w(qs.db.Where("bk_biz_id = ?", bkBizId))
}

// BkBizIdGt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) BkBizIdGt(bkBizId int) ResultTableQuerySet {
	return qs.w(qs.db.Where("bk_biz_id > ?", bkBizId))
}

// BkBizIdGte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) BkBizIdGte(bkBizId int) ResultTableQuerySet {
	return qs.w(qs.db.Where("bk_biz_id >= ?", bkBizId))
}

// BkBizIdIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) BkBizIdIn(bkBizId ...int) ResultTableQuerySet {
	if len(bkBizId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizId in BkBizIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id IN (?)", bkBizId))
}

// BkBizIdLt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) BkBizIdLt(bkBizId int) ResultTableQuerySet {
	return qs.w(qs.db.Where("bk_biz_id < ?", bkBizId))
}

// BkBizIdLte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) BkBizIdLte(bkBizId int) ResultTableQuerySet {
	return qs.w(qs.db.Where("bk_biz_id <= ?", bkBizId))
}

// BkBizIdNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) BkBizIdNe(bkBizId int) ResultTableQuerySet {
	return qs.w(qs.db.Where("bk_biz_id != ?", bkBizId))
}

// BkBizIdNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) BkBizIdNotIn(bkBizId ...int) ResultTableQuerySet {
	if len(bkBizId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizId in BkBizIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id NOT IN (?)", bkBizId))
}

// Count is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreateTimeEq(createTime time.Time) ResultTableQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreateTimeGt(createTime time.Time) ResultTableQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreateTimeGte(createTime time.Time) ResultTableQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreateTimeLt(createTime time.Time) ResultTableQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreateTimeLte(createTime time.Time) ResultTableQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreateTimeNe(createTime time.Time) ResultTableQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreatorEq(creator string) ResultTableQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreatorGt(creator string) ResultTableQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreatorGte(creator string) ResultTableQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreatorIn(creator ...string) ResultTableQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreatorLike(creator string) ResultTableQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreatorLt(creator string) ResultTableQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreatorLte(creator string) ResultTableQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreatorNe(creator string) ResultTableQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreatorNotIn(creator ...string) ResultTableQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) CreatorNotlike(creator string) ResultTableQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// DataLabelEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DataLabelEq(dataLabel string) ResultTableQuerySet {
	return qs.w(qs.db.Where("data_label = ?", dataLabel))
}

// DataLabelGt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DataLabelGt(dataLabel string) ResultTableQuerySet {
	return qs.w(qs.db.Where("data_label > ?", dataLabel))
}

// DataLabelGte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DataLabelGte(dataLabel string) ResultTableQuerySet {
	return qs.w(qs.db.Where("data_label >= ?", dataLabel))
}

// DataLabelIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DataLabelIn(dataLabel ...string) ResultTableQuerySet {
	if len(dataLabel) == 0 {
		qs.db.AddError(errors.New("must at least pass one dataLabel in DataLabelIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("data_label IN (?)", dataLabel))
}

// DataLabelLike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DataLabelLike(dataLabel string) ResultTableQuerySet {
	return qs.w(qs.db.Where("data_label LIKE ?", dataLabel))
}

// DataLabelLt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DataLabelLt(dataLabel string) ResultTableQuerySet {
	return qs.w(qs.db.Where("data_label < ?", dataLabel))
}

// DataLabelLte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DataLabelLte(dataLabel string) ResultTableQuerySet {
	return qs.w(qs.db.Where("data_label <= ?", dataLabel))
}

// DataLabelNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DataLabelNe(dataLabel string) ResultTableQuerySet {
	return qs.w(qs.db.Where("data_label != ?", dataLabel))
}

// DataLabelNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DataLabelNotIn(dataLabel ...string) ResultTableQuerySet {
	if len(dataLabel) == 0 {
		qs.db.AddError(errors.New("must at least pass one dataLabel in DataLabelNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("data_label NOT IN (?)", dataLabel))
}

// DataLabelNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DataLabelNotlike(dataLabel string) ResultTableQuerySet {
	return qs.w(qs.db.Where("data_label NOT LIKE ?", dataLabel))
}

// DefaultStorageEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DefaultStorageEq(defaultStorage string) ResultTableQuerySet {
	return qs.w(qs.db.Where("default_storage = ?", defaultStorage))
}

// DefaultStorageGt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DefaultStorageGt(defaultStorage string) ResultTableQuerySet {
	return qs.w(qs.db.Where("default_storage > ?", defaultStorage))
}

// DefaultStorageGte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DefaultStorageGte(defaultStorage string) ResultTableQuerySet {
	return qs.w(qs.db.Where("default_storage >= ?", defaultStorage))
}

// DefaultStorageIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DefaultStorageIn(defaultStorage ...string) ResultTableQuerySet {
	if len(defaultStorage) == 0 {
		qs.db.AddError(errors.New("must at least pass one defaultStorage in DefaultStorageIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("default_storage IN (?)", defaultStorage))
}

// DefaultStorageLike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DefaultStorageLike(defaultStorage string) ResultTableQuerySet {
	return qs.w(qs.db.Where("default_storage LIKE ?", defaultStorage))
}

// DefaultStorageLt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DefaultStorageLt(defaultStorage string) ResultTableQuerySet {
	return qs.w(qs.db.Where("default_storage < ?", defaultStorage))
}

// DefaultStorageLte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DefaultStorageLte(defaultStorage string) ResultTableQuerySet {
	return qs.w(qs.db.Where("default_storage <= ?", defaultStorage))
}

// DefaultStorageNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DefaultStorageNe(defaultStorage string) ResultTableQuerySet {
	return qs.w(qs.db.Where("default_storage != ?", defaultStorage))
}

// DefaultStorageNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DefaultStorageNotIn(defaultStorage ...string) ResultTableQuerySet {
	if len(defaultStorage) == 0 {
		qs.db.AddError(errors.New("must at least pass one defaultStorage in DefaultStorageNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("default_storage NOT IN (?)", defaultStorage))
}

// DefaultStorageNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DefaultStorageNotlike(defaultStorage string) ResultTableQuerySet {
	return qs.w(qs.db.Where("default_storage NOT LIKE ?", defaultStorage))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) Delete() error {
	return qs.db.Delete(ResultTable{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(ResultTable{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(ResultTable{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) GetUpdater() ResultTableUpdater {
	return NewResultTableUpdater(qs.db)
}

// IsCustomTableEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) IsCustomTableEq(isCustomTable bool) ResultTableQuerySet {
	return qs.w(qs.db.Where("is_custom_table = ?", isCustomTable))
}

// IsCustomTableIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) IsCustomTableIn(isCustomTable ...bool) ResultTableQuerySet {
	if len(isCustomTable) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCustomTable in IsCustomTableIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_custom_table IN (?)", isCustomTable))
}

// IsCustomTableNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) IsCustomTableNe(isCustomTable bool) ResultTableQuerySet {
	return qs.w(qs.db.Where("is_custom_table != ?", isCustomTable))
}

// IsCustomTableNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) IsCustomTableNotIn(isCustomTable ...bool) ResultTableQuerySet {
	if len(isCustomTable) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCustomTable in IsCustomTableNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_custom_table NOT IN (?)", isCustomTable))
}

// IsDeletedEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) IsDeletedEq(isDeleted bool) ResultTableQuerySet {
	return qs.w(qs.db.Where("is_deleted = ?", isDeleted))
}

// IsDeletedIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) IsDeletedIn(isDeleted ...bool) ResultTableQuerySet {
	if len(isDeleted) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDeleted in IsDeletedIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_deleted IN (?)", isDeleted))
}

// IsDeletedNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) IsDeletedNe(isDeleted bool) ResultTableQuerySet {
	return qs.w(qs.db.Where("is_deleted != ?", isDeleted))
}

// IsDeletedNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) IsDeletedNotIn(isDeleted ...bool) ResultTableQuerySet {
	if len(isDeleted) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDeleted in IsDeletedNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_deleted NOT IN (?)", isDeleted))
}

// IsEnableEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) IsEnableEq(isEnable bool) ResultTableQuerySet {
	return qs.w(qs.db.Where("is_enable = ?", isEnable))
}

// IsEnableIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) IsEnableIn(isEnable ...bool) ResultTableQuerySet {
	if len(isEnable) == 0 {
		qs.db.AddError(errors.New("must at least pass one isEnable in IsEnableIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_enable IN (?)", isEnable))
}

// IsEnableNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) IsEnableNe(isEnable bool) ResultTableQuerySet {
	return qs.w(qs.db.Where("is_enable != ?", isEnable))
}

// IsEnableNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) IsEnableNotIn(isEnable ...bool) ResultTableQuerySet {
	if len(isEnable) == 0 {
		qs.db.AddError(errors.New("must at least pass one isEnable in IsEnableNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_enable NOT IN (?)", isEnable))
}

// LabelEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LabelEq(label string) ResultTableQuerySet {
	return qs.w(qs.db.Where("label = ?", label))
}

// LabelGt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LabelGt(label string) ResultTableQuerySet {
	return qs.w(qs.db.Where("label > ?", label))
}

// LabelGte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LabelGte(label string) ResultTableQuerySet {
	return qs.w(qs.db.Where("label >= ?", label))
}

// LabelIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LabelIn(label ...string) ResultTableQuerySet {
	if len(label) == 0 {
		qs.db.AddError(errors.New("must at least pass one label in LabelIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("label IN (?)", label))
}

// LabelLike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LabelLike(label string) ResultTableQuerySet {
	return qs.w(qs.db.Where("label LIKE ?", label))
}

// LabelLt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LabelLt(label string) ResultTableQuerySet {
	return qs.w(qs.db.Where("label < ?", label))
}

// LabelLte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LabelLte(label string) ResultTableQuerySet {
	return qs.w(qs.db.Where("label <= ?", label))
}

// LabelNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LabelNe(label string) ResultTableQuerySet {
	return qs.w(qs.db.Where("label != ?", label))
}

// LabelNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LabelNotIn(label ...string) ResultTableQuerySet {
	if len(label) == 0 {
		qs.db.AddError(errors.New("must at least pass one label in LabelNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("label NOT IN (?)", label))
}

// LabelNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LabelNotlike(label string) ResultTableQuerySet {
	return qs.w(qs.db.Where("label NOT LIKE ?", label))
}

// LastModifyTimeEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyTimeEq(lastModifyTime time.Time) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_time = ?", lastModifyTime))
}

// LastModifyTimeGt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyTimeGt(lastModifyTime time.Time) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_time > ?", lastModifyTime))
}

// LastModifyTimeGte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyTimeGte(lastModifyTime time.Time) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_time >= ?", lastModifyTime))
}

// LastModifyTimeLt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyTimeLt(lastModifyTime time.Time) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_time < ?", lastModifyTime))
}

// LastModifyTimeLte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyTimeLte(lastModifyTime time.Time) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_time <= ?", lastModifyTime))
}

// LastModifyTimeNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyTimeNe(lastModifyTime time.Time) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_time != ?", lastModifyTime))
}

// LastModifyUserEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyUserEq(lastModifyUser string) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_user = ?", lastModifyUser))
}

// LastModifyUserGt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyUserGt(lastModifyUser string) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_user > ?", lastModifyUser))
}

// LastModifyUserGte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyUserGte(lastModifyUser string) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_user >= ?", lastModifyUser))
}

// LastModifyUserIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyUserIn(lastModifyUser ...string) ResultTableQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user IN (?)", lastModifyUser))
}

// LastModifyUserLike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyUserLike(lastModifyUser string) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_user LIKE ?", lastModifyUser))
}

// LastModifyUserLt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyUserLt(lastModifyUser string) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_user < ?", lastModifyUser))
}

// LastModifyUserLte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyUserLte(lastModifyUser string) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_user <= ?", lastModifyUser))
}

// LastModifyUserNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyUserNe(lastModifyUser string) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_user != ?", lastModifyUser))
}

// LastModifyUserNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyUserNotIn(lastModifyUser ...string) ResultTableQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user NOT IN (?)", lastModifyUser))
}

// LastModifyUserNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) LastModifyUserNotlike(lastModifyUser string) ResultTableQuerySet {
	return qs.w(qs.db.Where("last_modify_user NOT LIKE ?", lastModifyUser))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) Limit(limit int) ResultTableQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) Offset(offset int) ResultTableQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs ResultTableQuerySet) One(ret *ResultTable) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkBizId is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByBkBizId() ResultTableQuerySet {
	return qs.w(qs.db.Order("bk_biz_id ASC"))
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByCreateTime() ResultTableQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByCreator() ResultTableQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByDataLabel is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByDataLabel() ResultTableQuerySet {
	return qs.w(qs.db.Order("data_label ASC"))
}

// OrderAscByDefaultStorage is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByDefaultStorage() ResultTableQuerySet {
	return qs.w(qs.db.Order("default_storage ASC"))
}

// OrderAscByIsCustomTable is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByIsCustomTable() ResultTableQuerySet {
	return qs.w(qs.db.Order("is_custom_table ASC"))
}

// OrderAscByIsDeleted is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByIsDeleted() ResultTableQuerySet {
	return qs.w(qs.db.Order("is_deleted ASC"))
}

// OrderAscByIsEnable is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByIsEnable() ResultTableQuerySet {
	return qs.w(qs.db.Order("is_enable ASC"))
}

// OrderAscByLabel is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByLabel() ResultTableQuerySet {
	return qs.w(qs.db.Order("label ASC"))
}

// OrderAscByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByLastModifyTime() ResultTableQuerySet {
	return qs.w(qs.db.Order("last_modify_time ASC"))
}

// OrderAscByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByLastModifyUser() ResultTableQuerySet {
	return qs.w(qs.db.Order("last_modify_user ASC"))
}

// OrderAscBySchemaType is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscBySchemaType() ResultTableQuerySet {
	return qs.w(qs.db.Order("schema_type ASC"))
}

// OrderAscByTableId is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByTableId() ResultTableQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderAscByTableNameZh is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderAscByTableNameZh() ResultTableQuerySet {
	return qs.w(qs.db.Order("table_name_zh ASC"))
}

// OrderDescByBkBizId is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByBkBizId() ResultTableQuerySet {
	return qs.w(qs.db.Order("bk_biz_id DESC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByCreateTime() ResultTableQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByCreator() ResultTableQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByDataLabel is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByDataLabel() ResultTableQuerySet {
	return qs.w(qs.db.Order("data_label DESC"))
}

// OrderDescByDefaultStorage is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByDefaultStorage() ResultTableQuerySet {
	return qs.w(qs.db.Order("default_storage DESC"))
}

// OrderDescByIsCustomTable is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByIsCustomTable() ResultTableQuerySet {
	return qs.w(qs.db.Order("is_custom_table DESC"))
}

// OrderDescByIsDeleted is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByIsDeleted() ResultTableQuerySet {
	return qs.w(qs.db.Order("is_deleted DESC"))
}

// OrderDescByIsEnable is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByIsEnable() ResultTableQuerySet {
	return qs.w(qs.db.Order("is_enable DESC"))
}

// OrderDescByLabel is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByLabel() ResultTableQuerySet {
	return qs.w(qs.db.Order("label DESC"))
}

// OrderDescByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByLastModifyTime() ResultTableQuerySet {
	return qs.w(qs.db.Order("last_modify_time DESC"))
}

// OrderDescByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByLastModifyUser() ResultTableQuerySet {
	return qs.w(qs.db.Order("last_modify_user DESC"))
}

// OrderDescBySchemaType is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescBySchemaType() ResultTableQuerySet {
	return qs.w(qs.db.Order("schema_type DESC"))
}

// OrderDescByTableId is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByTableId() ResultTableQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// OrderDescByTableNameZh is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) OrderDescByTableNameZh() ResultTableQuerySet {
	return qs.w(qs.db.Order("table_name_zh DESC"))
}

// SchemaTypeEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) SchemaTypeEq(schemaType string) ResultTableQuerySet {
	return qs.w(qs.db.Where("schema_type = ?", schemaType))
}

// SchemaTypeGt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) SchemaTypeGt(schemaType string) ResultTableQuerySet {
	return qs.w(qs.db.Where("schema_type > ?", schemaType))
}

// SchemaTypeGte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) SchemaTypeGte(schemaType string) ResultTableQuerySet {
	return qs.w(qs.db.Where("schema_type >= ?", schemaType))
}

// SchemaTypeIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) SchemaTypeIn(schemaType ...string) ResultTableQuerySet {
	if len(schemaType) == 0 {
		qs.db.AddError(errors.New("must at least pass one schemaType in SchemaTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("schema_type IN (?)", schemaType))
}

// SchemaTypeLike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) SchemaTypeLike(schemaType string) ResultTableQuerySet {
	return qs.w(qs.db.Where("schema_type LIKE ?", schemaType))
}

// SchemaTypeLt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) SchemaTypeLt(schemaType string) ResultTableQuerySet {
	return qs.w(qs.db.Where("schema_type < ?", schemaType))
}

// SchemaTypeLte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) SchemaTypeLte(schemaType string) ResultTableQuerySet {
	return qs.w(qs.db.Where("schema_type <= ?", schemaType))
}

// SchemaTypeNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) SchemaTypeNe(schemaType string) ResultTableQuerySet {
	return qs.w(qs.db.Where("schema_type != ?", schemaType))
}

// SchemaTypeNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) SchemaTypeNotIn(schemaType ...string) ResultTableQuerySet {
	if len(schemaType) == 0 {
		qs.db.AddError(errors.New("must at least pass one schemaType in SchemaTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("schema_type NOT IN (?)", schemaType))
}

// SchemaTypeNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) SchemaTypeNotlike(schemaType string) ResultTableQuerySet {
	return qs.w(qs.db.Where("schema_type NOT LIKE ?", schemaType))
}

// TableIdEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableIdEq(tableId string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableId))
}

// TableIdGt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableIdGt(tableId string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableId))
}

// TableIdGte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableIdGte(tableId string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableId))
}

// TableIdIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableIdIn(tableId ...string) ResultTableQuerySet {
	if len(tableId) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableId in TableIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableId))
}

// TableIdLike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableIdLike(tableId string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableId))
}

// TableIdLt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableIdLt(tableId string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableId))
}

// TableIdLte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableIdLte(tableId string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableId))
}

// TableIdNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableIdNe(tableId string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableId))
}

// TableIdNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableIdNotIn(tableId ...string) ResultTableQuerySet {
	if len(tableId) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableId in TableIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableId))
}

// TableIdNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableIdNotlike(tableId string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableId))
}

// TableNameZhEq is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableNameZhEq(tableNameZh string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_name_zh = ?", tableNameZh))
}

// TableNameZhGt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableNameZhGt(tableNameZh string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_name_zh > ?", tableNameZh))
}

// TableNameZhGte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableNameZhGte(tableNameZh string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_name_zh >= ?", tableNameZh))
}

// TableNameZhIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableNameZhIn(tableNameZh ...string) ResultTableQuerySet {
	if len(tableNameZh) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableNameZh in TableNameZhIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_name_zh IN (?)", tableNameZh))
}

// TableNameZhLike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableNameZhLike(tableNameZh string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_name_zh LIKE ?", tableNameZh))
}

// TableNameZhLt is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableNameZhLt(tableNameZh string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_name_zh < ?", tableNameZh))
}

// TableNameZhLte is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableNameZhLte(tableNameZh string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_name_zh <= ?", tableNameZh))
}

// TableNameZhNe is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableNameZhNe(tableNameZh string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_name_zh != ?", tableNameZh))
}

// TableNameZhNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableNameZhNotIn(tableNameZh ...string) ResultTableQuerySet {
	if len(tableNameZh) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableNameZh in TableNameZhNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_name_zh NOT IN (?)", tableNameZh))
}

// TableNameZhNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableQuerySet) TableNameZhNotlike(tableNameZh string) ResultTableQuerySet {
	return qs.w(qs.db.Where("table_name_zh NOT LIKE ?", tableNameZh))
}

// SetBkBizId is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetBkBizId(bkBizId int) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.BkBizId)] = bkBizId
	return u
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetCreateTime(createTime time.Time) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetCreator(creator string) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.Creator)] = creator
	return u
}

// SetDataLabel is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetDataLabel(dataLabel string) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.DataLabel)] = dataLabel
	return u
}

// SetDefaultStorage is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetDefaultStorage(defaultStorage string) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.DefaultStorage)] = defaultStorage
	return u
}

// SetIsCustomTable is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetIsCustomTable(isCustomTable bool) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.IsCustomTable)] = isCustomTable
	return u
}

// SetIsDeleted is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetIsDeleted(isDeleted bool) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.IsDeleted)] = isDeleted
	return u
}

// SetIsEnable is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetIsEnable(isEnable bool) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.IsEnable)] = isEnable
	return u
}

// SetLabel is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetLabel(label string) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.Label)] = label
	return u
}

// SetLastModifyTime is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetLastModifyTime(lastModifyTime time.Time) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.LastModifyTime)] = lastModifyTime
	return u
}

// SetLastModifyUser is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetLastModifyUser(lastModifyUser string) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.LastModifyUser)] = lastModifyUser
	return u
}

// SetSchemaType is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetSchemaType(schemaType string) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.SchemaType)] = schemaType
	return u
}

// SetTableId is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetTableId(tableId string) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.TableId)] = tableId
	return u
}

// SetTableNameZh is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) SetTableNameZh(tableNameZh string) ResultTableUpdater {
	u.fields[string(ResultTableDBSchema.TableNameZh)] = tableNameZh
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u ResultTableUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set ResultTableQuerySet

// ===== BEGIN of ResultTable modifiers

// ResultTableDBSchemaField describes database schema field. It requires for method 'Update'
type ResultTableDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f ResultTableDBSchemaField) String() string {
	return string(f)
}

// ResultTableDBSchema stores db field names of ResultTable
var ResultTableDBSchema = struct {
	TableId        ResultTableDBSchemaField
	TableNameZh    ResultTableDBSchemaField
	IsCustomTable  ResultTableDBSchemaField
	SchemaType     ResultTableDBSchemaField
	DefaultStorage ResultTableDBSchemaField
	Creator        ResultTableDBSchemaField
	CreateTime     ResultTableDBSchemaField
	LastModifyUser ResultTableDBSchemaField
	LastModifyTime ResultTableDBSchemaField
	BkBizId        ResultTableDBSchemaField
	IsDeleted      ResultTableDBSchemaField
	Label          ResultTableDBSchemaField
	IsEnable       ResultTableDBSchemaField
	DataLabel      ResultTableDBSchemaField
}{

	TableId:        ResultTableDBSchemaField("table_id"),
	TableNameZh:    ResultTableDBSchemaField("table_name_zh"),
	IsCustomTable:  ResultTableDBSchemaField("is_custom_table"),
	SchemaType:     ResultTableDBSchemaField("schema_type"),
	DefaultStorage: ResultTableDBSchemaField("default_storage"),
	Creator:        ResultTableDBSchemaField("creator"),
	CreateTime:     ResultTableDBSchemaField("create_time"),
	LastModifyUser: ResultTableDBSchemaField("last_modify_user"),
	LastModifyTime: ResultTableDBSchemaField("last_modify_time"),
	BkBizId:        ResultTableDBSchemaField("bk_biz_id"),
	IsDeleted:      ResultTableDBSchemaField("is_deleted"),
	Label:          ResultTableDBSchemaField("label"),
	IsEnable:       ResultTableDBSchemaField("is_enable"),
	DataLabel:      ResultTableDBSchemaField("data_label"),
}

// Update updates ResultTable fields by primary key
// nolint: dupl
func (o *ResultTable) Update(db *gorm.DB, fields ...ResultTableDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"table_id":         o.TableId,
		"table_name_zh":    o.TableNameZh,
		"is_custom_table":  o.IsCustomTable,
		"schema_type":      o.SchemaType,
		"default_storage":  o.DefaultStorage,
		"creator":          o.Creator,
		"create_time":      o.CreateTime,
		"last_modify_user": o.LastModifyUser,
		"last_modify_time": o.LastModifyTime,
		"bk_biz_id":        o.BkBizId,
		"is_deleted":       o.IsDeleted,
		"label":            o.Label,
		"is_enable":        o.IsEnable,
		"data_label":       o.DataLabel,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update ResultTable %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// ResultTableUpdater is an ResultTable updates manager
type ResultTableUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewResultTableUpdater creates new ResultTable updater
// nolint: dupl
func NewResultTableUpdater(db *gorm.DB) ResultTableUpdater {
	return ResultTableUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&ResultTable{}),
	}
}

// ===== END of ResultTable modifiers

// ===== END of all query sets
