// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package customreport

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDimensions(t *testing.T) {
	var dimensionObj = []string{"d1", "d2", "d3", "d4"}
	event := &Event{
		EventID:       123,
		EventGroupID:  1,
		EventName:     "test_event",
		DimensionList: `["d1","d2","d3","d4"]`,
	}

	assert.True(t, reflect.DeepEqual(dimensionObj, event.GetDimensionList()))

	dimensionObj = dimensionObj[:1]
	err := event.SetDimensionList(dimensionObj)
	assert.Nil(t, err)
	assert.True(t, reflect.DeepEqual(dimensionObj, event.GetDimensionList()))
}
