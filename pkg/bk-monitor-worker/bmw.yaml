service:
  task:
    gin_mode: release
    listen: 127.0.0.1
    port: 10211
  worker:
    gin_mode: release
    listen: 127.0.0.1
    port: 10212
    goroutine_limit:
      refresh_datasource: 10
      refresh_influxdb_route: 10
      refresh_es_storage: 10
      refresh_event_dimension: 10
broker:
  redis:
    type: "single"
    host: "127.0.0.1"
    port: 6379
    password: 123456
    database: 0
store:
  redis:
    type: "single"
    host: "127.0.0.1"
    port: 6379
    password: 123456
    database: 0
    periodic_task_key: "bmw:periodic_task"
    channel_name: "bmw:channel:periodic_task"
  dependent_redis:
    type: "single"
    host: "127.0.0.1"
    port: 6379
    password: 123456
    database: 0
  database:
    type: mysql
    host: "127.0.0.1"
    port: 3306
    user: root
    password: "123456"
    db_name: "test"
    charset: utf8
    max_idle_conns: 10
    max_open_conns: 100
    debug_mode: false
  consul:
    basic_path: "bkmonitorv3_enterprise_production"
log:
  filename: "./bmw.log"
  max_size: 200
  max_age: 1
  max_backups: 5
  level: "info"
metric_dimension:
  metric_key_prefix: "bkmonitor:metrics_"
  metric_dimension_key_prefix: "bkmonitor:metric_dimensions_"
  max_metrics_fetch_step: 500
  time_series_metric_expired_days: 30
aes:
  key: ""
test:
  database:
    type: mysql
    host: "127.0.0.1"
    port: 3306
    user: root
    password: "123456"
    db_name: "testdb"
bk_api:
  api_url: ""
  stage: "prod"
  app_code: ""
  app_secret: ""