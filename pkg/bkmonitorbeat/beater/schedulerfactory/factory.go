// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package schedulerfactory

import (
	"errors"
	"fmt"

	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/bkmonitorbeat/configs"
	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/bkmonitorbeat/define"
)

// Errors
var (
	ErrSchedulerTypeNotFound = errors.New("scheduler type not found")
)

type schedulerNew func(define.Beater, define.Config) define.Scheduler

var mappings = make(map[string]schedulerNew)

// Register :
func Register(name string, f schedulerNew) {
	mappings[name] = f
}

// New :
func New(beater define.Beater, conf *configs.Config, schedulerType string) define.Scheduler {
	if schedulerType == "" {
		schedulerType = conf.Mode
	}

	f, ok := mappings[schedulerType]
	if !ok {
		panic(fmt.Errorf("scheduler[%v] not found", conf.Mode))
	}
	return f(beater, conf)
}
